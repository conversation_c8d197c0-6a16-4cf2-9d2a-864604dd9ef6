{"name": "@cypress/mount-utils", "version": "0.0.0-development", "description": "Shared utilities for the various component testing adapters", "main": "dist/index.js", "scripts": {"build": "tsc || echo 'built, with type errors'", "postbuild": "node ../../scripts/sync-exported-npm-with-cli.js", "check-ts": "tsc --noEmit", "lint": "eslint --ext .js,.ts,.json, .", "watch": "tsc -w"}, "dependencies": {}, "devDependencies": {"@rollup/plugin-commonjs": "^17.1.0", "@rollup/plugin-node-resolve": "^11.1.1", "rollup": "3.7.3", "rollup-plugin-dts": "5.0.0", "rollup-plugin-typescript2": "^0.29.0", "typescript": "~5.4.5"}, "files": ["dist"], "types": "dist/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cypress-io/cypress.git"}, "homepage": "https://github.com/cypress-io/cypress/tree/develop/npm/mount-utils#readme", "bugs": "https://github.com/cypress-io/cypress/issues/new?template=1-bug-report.md", "publishConfig": {"access": "public"}, "nx": {"targets": {"build": {"outputs": ["{workspaceRoot}/cli/mount-utils", "{projectRoot}/dist"]}}}}