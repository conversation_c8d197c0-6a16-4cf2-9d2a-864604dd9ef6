# @cypress/react

Mount React components in the open source [Cypress.io](https://www.cypress.io/) test runner

> **Note:** This package is bundled with the `cypress` package and should not need to be installed separately. See the [React Component Testing Docs](https://docs.cypress.io/guides/component-testing/react/overview) for mounting React components. Installing and importing `mount` from `@cypress/react` should only be done for advanced use-cases.

## Requirements

- React 18.0.0+ (Cypress 13 and under supports React 16 and 17)

## Development

Run `yarn build` to compile and sync packages to the `cypress` cli package.

Run `yarn cy:open` to open Cypress component testing against real-world examples.

Run `yarn test` to execute headless Cypress tests.

## [Changelog](./CHANGELOG.md)
