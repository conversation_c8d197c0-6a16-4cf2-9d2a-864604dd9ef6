{"name": "@cypress/react", "version": "0.0.0-development", "description": "Test React components using Cypress", "main": "dist/cypress-react.cjs.js", "scripts": {"build": "rimraf dist && rollup -c rollup.config.mjs", "postbuild": "node ../../scripts/sync-exported-npm-with-cli.js", "check-ts": "tsc --noEmit", "cy:open": "node ../../scripts/cypress.js open --component", "cy:open:debug": "node --inspect-brk ../../scripts/start.js --component-testing --run-project ${PWD}", "cy:run": "node ../../scripts/cypress.js run --component", "cy:run:debug": "node --inspect-brk ../../scripts/start.js --component-testing --run-project ${PWD}", "lint": "eslint --ext .js,.jsx,.ts,.tsx,.json, .", "test": "yarn cy:run", "watch": "yarn build --watch --watch.exclude ./dist/**/*"}, "devDependencies": {"@cypress/mount-utils": "0.0.0-development", "@types/semver": "7.5.8", "@vitejs/plugin-react": "4.3.3", "axios": "1.7.7", "cypress": "0.0.0-development", "prop-types": "15.8.1", "react": "18.3.1", "react-dom": "18.3.1", "react-router": "6.28.0", "react-router-dom": "6.28.0", "semver": "^7.7.1", "typescript": "~5.4.5", "vite": "5.4.18", "vite-plugin-require-transform": "1.0.12"}, "peerDependencies": {"@types/react": "^18 || ^19", "@types/react-dom": "^18 || ^19", "cypress": "*", "react": "^18 || ^19", "react-dom": "^18 || ^19"}, "files": ["dist"], "types": "dist/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cypress-io/cypress.git"}, "homepage": "https://github.com/cypress-io/cypress/blob/develop/npm/react/#readme", "bugs": "https://github.com/cypress-io/cypress/issues/new?assignees=&labels=npm%3A%20%40cypress%2Freact&template=1-bug-report.md&title=", "keywords": ["react", "cypress", "cypress-io", "test", "testing"], "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "social": "@dmtrKovalenko"}, {"name": "<PERSON>", "social": "@brian-mann"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "social": "@elevatebart"}, {"name": "<PERSON><PERSON><PERSON>", "social": "@lmiller1990"}, {"name": "<PERSON>", "social": "@_<PERSON><PERSON><PERSON><PERSON>"}], "unpkg": "dist/cypress-react.browser.js", "module": "dist/cypress-react.esm-bundler.js", "peerDependenciesMeta": {"@types/react": {"optional": true}}, "publishConfig": {"access": "public"}, "nx": {"targets": {"build": {"outputs": ["{workspaceRoot}/cli/react", "{projectRoot}/dist"]}}}, "standard": {"globals": ["Cypress", "cy", "expect"]}}