{"name": "@cypress/svelte", "version": "0.0.0-development", "description": "Browser-based Component Testing for Svelte.js with Cypress.io 🧡", "main": "dist/cypress-svelte.cjs.js", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup -c rollup.config.mjs", "postbuild": "node ../../scripts/sync-exported-npm-with-cli.js", "check-ts": "tsc --noEmit", "lint": "eslint --ext .js,.jsx,.ts,.tsx,.json, ."}, "devDependencies": {"@cypress/mount-utils": "0.0.0-development", "svelte": "^5.4.0", "typescript": "~5.4.5"}, "peerDependencies": {"cypress": ">=10.6.0", "svelte": ">=5.0.0"}, "files": ["dist/**/*"], "types": "dist/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cypress-io/cypress.git"}, "homepage": "https://github.com/cypress-io/cypress/blob/develop/npm/svelte/#readme", "bugs": "https://github.com/cypress-io/cypress/issues/new?assignees=&labels=npm%3A%20%40cypress%2Fsvelte&template=1-bug-report.md&title=", "keywords": ["cypress", "svelte", "testing", "component testing"], "module": "dist/cypress-svelte.esm-bundler.js", "publishConfig": {"access": "public"}, "nx": {"targets": {"build": {"outputs": ["{workspaceRoot}/cli/svelte", "{projectRoot}/dist"]}}, "implicitDependencies": ["!cypress"]}}