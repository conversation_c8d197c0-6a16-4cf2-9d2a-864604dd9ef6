// AUTO-GENERATED: do not modify this file directly.
// If you need to make changes, modify generate-fp.ts (if necessary), then open a terminal in types/lodash/scripts, and do:
// npm install && npm run generate

import lodash = require("./index");

export = _;

declare const _: _.LoDashFp;
declare namespace _ {
    interface LodashAdd {
        (augend: number): LodashAdd1x1;
        (augend: lodash.__, addend: number): LodashAdd1x2;
        (augend: number, addend: number): number;
    }
    type LodashAdd1x1 = (addend: number) => number;
    type LodashAdd1x2 = (augend: number) => number;
    interface LodashAfter {
        <TFunc extends (...args: any[]) => any>(func: TFunc): LodashAfter1x1<TFunc>;
        (func: lodash.__, n: number): LodashAfter1x2;
        <TFunc extends (...args: any[]) => any>(func: TFunc, n: number): TFunc;
    }
    type LodashAfter1x1<TFunc extends (...args: any[]) => any> = (n: number) => TFunc;
    type LodashAfter1x2 = <TFunc extends (...args: any[]) => any>(func: TFunc) => TFunc;
    interface LodashEvery {
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>): LodashEvery1x1<T>;
        <T>(predicate: lodash.__, collection: lodash.List<T> | null | undefined): LodashEvery1x2<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, collection: lodash.List<T> | null | undefined): boolean;
        <T extends object>(predicate: lodash.__, collection: T | null | undefined): LodashEvery2x2<T>;
        <T extends object>(predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>, collection: T | null | undefined): boolean;
    }
    type LodashEvery1x1<T> = (collection: lodash.List<T> | object | null | undefined) => boolean;
    type LodashEvery1x2<T> = (predicate: lodash.ValueIterateeCustom<T, boolean>) => boolean;
    type LodashEvery2x2<T> = (predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>) => boolean;
    type LodashOverEvery = <T>(predicates: lodash.Many<(...args: T[]) => boolean>) => (...args: T[]) => boolean;
    type LodashConstant = <T>(value: T) => () => T;
    interface LodashSome {
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>): LodashSome1x1<T>;
        <T>(predicate: lodash.__, collection: lodash.List<T> | null | undefined): LodashSome1x2<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, collection: lodash.List<T> | null | undefined): boolean;
        <T extends object>(predicate: lodash.__, collection: T | null | undefined): LodashSome2x2<T>;
        <T extends object>(predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>, collection: T | null | undefined): boolean;
    }
    type LodashSome1x1<T> = (collection: lodash.List<T> | object | null | undefined) => boolean;
    type LodashSome1x2<T> = (predicate: lodash.ValueIterateeCustom<T, boolean>) => boolean;
    type LodashSome2x2<T> = (predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>) => boolean;
    type LodashOverSome = <T>(predicates: lodash.Many<(...args: T[]) => boolean>) => (...args: T[]) => boolean;
    type LodashApply = <TResult>(func: (...args: any[]) => TResult) => (...args: any[]) => TResult;
    interface LodashAry {
        (n: number): LodashAry1x1;
        (n: lodash.__, func: (...args: any[]) => any): LodashAry1x2;
        (n: number, func: (...args: any[]) => any): (...args: any[]) => any;
    }
    type LodashAry1x1 = (func: (...args: any[]) => any) => (...args: any[]) => any;
    type LodashAry1x2 = (n: number) => (...args: any[]) => any;
    interface LodashAssign {
        <TObject>(object: TObject): LodashAssign1x1<TObject>;
        <TSource>(object: lodash.__, source: TSource): LodashAssign1x2<TSource>;
        <TObject, TSource>(object: TObject, source: TSource): TObject & TSource;
    }
    type LodashAssign1x1<TObject> = <TSource>(source: TSource) => TObject & TSource;
    type LodashAssign1x2<TSource> = <TObject>(object: TObject) => TObject & TSource;
    interface LodashAssignAll {
        <TObject, TSource>(object: [TObject, TSource]): TObject & TSource;
        <TObject, TSource1, TSource2>(object: [TObject, TSource1, TSource2]): TObject & TSource1 & TSource2;
        <TObject, TSource1, TSource2, TSource3>(object: [TObject, TSource1, TSource2, TSource3]): TObject & TSource1 & TSource2 & TSource3;
        <TObject, TSource1, TSource2, TSource3, TSource4>(object: [TObject, TSource1, TSource2, TSource3, TSource4]): TObject & TSource1 & TSource2 & TSource3 & TSource4;
        <TObject>(object: [TObject]): TObject;
        (object: ReadonlyArray<any>): any;
    }
    interface LodashAssignAllWith {
        (customizer: lodash.AssignCustomizer): LodashAssignAllWith1x1;
        (customizer: lodash.__, args: ReadonlyArray<any>): LodashAssignAllWith1x2;
        (customizer: lodash.AssignCustomizer, args: ReadonlyArray<any>): any;
    }
    type LodashAssignAllWith1x1 = (args: ReadonlyArray<any>) => any;
    type LodashAssignAllWith1x2 = (customizer: lodash.AssignCustomizer) => any;
    interface LodashAssignIn {
        <TObject>(object: TObject): LodashAssignIn1x1<TObject>;
        <TSource>(object: lodash.__, source: TSource): LodashAssignIn1x2<TSource>;
        <TObject, TSource>(object: TObject, source: TSource): TObject & TSource;
    }
    type LodashAssignIn1x1<TObject> = <TSource>(source: TSource) => TObject & TSource;
    type LodashAssignIn1x2<TSource> = <TObject>(object: TObject) => TObject & TSource;
    interface LodashAssignInAll {
        <TObject, TSource>(object: [TObject, TSource]): TObject & TSource;
        <TObject, TSource1, TSource2>(object: [TObject, TSource1, TSource2]): TObject & TSource1 & TSource2;
        <TObject, TSource1, TSource2, TSource3>(object: [TObject, TSource1, TSource2, TSource3]): TObject & TSource1 & TSource2 & TSource3;
        <TObject, TSource1, TSource2, TSource3, TSource4>(object: [TObject, TSource1, TSource2, TSource3, TSource4]): TObject & TSource1 & TSource2 & TSource3 & TSource4;
        <TObject>(object: [TObject]): TObject;
        <TResult>(object: ReadonlyArray<any>): TResult;
    }
    interface LodashAssignInAllWith {
        (customizer: lodash.AssignCustomizer): LodashAssignInAllWith1x1;
        (customizer: lodash.__, args: ReadonlyArray<any>): LodashAssignInAllWith1x2;
        (customizer: lodash.AssignCustomizer, args: ReadonlyArray<any>): any;
    }
    type LodashAssignInAllWith1x1 = (args: ReadonlyArray<any>) => any;
    type LodashAssignInAllWith1x2 = (customizer: lodash.AssignCustomizer) => any;
    interface LodashAssignInWith {
        (customizer: lodash.AssignCustomizer): LodashAssignInWith1x1;
        <TObject>(customizer: lodash.__, object: TObject): LodashAssignInWith1x2<TObject>;
        <TObject>(customizer: lodash.AssignCustomizer, object: TObject): LodashAssignInWith1x3<TObject>;
        <TSource>(customizer: lodash.__, object: lodash.__, source: TSource): LodashAssignInWith1x4<TSource>;
        <TSource>(customizer: lodash.AssignCustomizer, object: lodash.__, source: TSource): LodashAssignInWith1x5<TSource>;
        <TObject, TSource>(customizer: lodash.__, object: TObject, source: TSource): LodashAssignInWith1x6<TObject, TSource>;
        <TObject, TSource>(customizer: lodash.AssignCustomizer, object: TObject, source: TSource): TObject & TSource;
    }
    interface LodashAssignInWith1x1 {
        <TObject>(object: TObject): LodashAssignInWith1x3<TObject>;
        <TSource>(object: lodash.__, source: TSource): LodashAssignInWith1x5<TSource>;
        <TObject, TSource>(object: TObject, source: TSource): TObject & TSource;
    }
    interface LodashAssignInWith1x2<TObject> {
        (customizer: lodash.AssignCustomizer): LodashAssignInWith1x3<TObject>;
        <TSource>(customizer: lodash.__, source: TSource): LodashAssignInWith1x6<TObject, TSource>;
        <TSource>(customizer: lodash.AssignCustomizer, source: TSource): TObject & TSource;
    }
    type LodashAssignInWith1x3<TObject> = <TSource>(source: TSource) => TObject & TSource;
    interface LodashAssignInWith1x4<TSource> {
        (customizer: lodash.AssignCustomizer): LodashAssignInWith1x5<TSource>;
        <TObject>(customizer: lodash.__, object: TObject): LodashAssignInWith1x6<TObject, TSource>;
        <TObject>(customizer: lodash.AssignCustomizer, object: TObject): TObject & TSource;
    }
    type LodashAssignInWith1x5<TSource> = <TObject>(object: TObject) => TObject & TSource;
    type LodashAssignInWith1x6<TObject, TSource> = (customizer: lodash.AssignCustomizer) => TObject & TSource;
    interface LodashAssignWith {
        (customizer: lodash.AssignCustomizer): LodashAssignWith1x1;
        <TObject>(customizer: lodash.__, object: TObject): LodashAssignWith1x2<TObject>;
        <TObject>(customizer: lodash.AssignCustomizer, object: TObject): LodashAssignWith1x3<TObject>;
        <TSource>(customizer: lodash.__, object: lodash.__, source: TSource): LodashAssignWith1x4<TSource>;
        <TSource>(customizer: lodash.AssignCustomizer, object: lodash.__, source: TSource): LodashAssignWith1x5<TSource>;
        <TObject, TSource>(customizer: lodash.__, object: TObject, source: TSource): LodashAssignWith1x6<TObject, TSource>;
        <TObject, TSource>(customizer: lodash.AssignCustomizer, object: TObject, source: TSource): TObject & TSource;
    }
    interface LodashAssignWith1x1 {
        <TObject>(object: TObject): LodashAssignWith1x3<TObject>;
        <TSource>(object: lodash.__, source: TSource): LodashAssignWith1x5<TSource>;
        <TObject, TSource>(object: TObject, source: TSource): TObject & TSource;
    }
    interface LodashAssignWith1x2<TObject> {
        (customizer: lodash.AssignCustomizer): LodashAssignWith1x3<TObject>;
        <TSource>(customizer: lodash.__, source: TSource): LodashAssignWith1x6<TObject, TSource>;
        <TSource>(customizer: lodash.AssignCustomizer, source: TSource): TObject & TSource;
    }
    type LodashAssignWith1x3<TObject> = <TSource>(source: TSource) => TObject & TSource;
    interface LodashAssignWith1x4<TSource> {
        (customizer: lodash.AssignCustomizer): LodashAssignWith1x5<TSource>;
        <TObject>(customizer: lodash.__, object: TObject): LodashAssignWith1x6<TObject, TSource>;
        <TObject>(customizer: lodash.AssignCustomizer, object: TObject): TObject & TSource;
    }
    type LodashAssignWith1x5<TSource> = <TObject>(object: TObject) => TObject & TSource;
    type LodashAssignWith1x6<TObject, TSource> = (customizer: lodash.AssignCustomizer) => TObject & TSource;
    interface LodashSet {
        (path: lodash.PropertyPath): LodashSet1x1;
        (path: lodash.__, value: any): LodashSet1x2;
        (path: lodash.PropertyPath, value: any): LodashSet1x3;
        <T extends object>(path: lodash.__, value: lodash.__, object: T): LodashSet1x4<T>;
        <T extends object>(path: lodash.PropertyPath, value: lodash.__, object: T): LodashSet1x5<T>;
        <T extends object>(path: lodash.__, value: any, object: T): LodashSet1x6<T>;
        <T extends object>(path: lodash.PropertyPath, value: any, object: T): T;
        (path: lodash.__, value: lodash.__, object: object): LodashSet2x4;
        (path: lodash.PropertyPath, value: lodash.__, object: object): LodashSet2x5;
        (path: lodash.__, value: any, object: object): LodashSet2x6;
        <TResult>(path: lodash.PropertyPath, value: any, object: object): TResult;
    }
    interface LodashSet1x1 {
        (value: any): LodashSet1x3;
        <T extends object>(value: lodash.__, object: T): LodashSet1x5<T>;
        <T extends object>(value: any, object: T): T;
        (value: lodash.__, object: object): LodashSet2x5;
        <TResult>(value: any, object: object): TResult;
    }
    interface LodashSet1x2 {
        (path: lodash.PropertyPath): LodashSet1x3;
        <T extends object>(path: lodash.__, object: T): LodashSet1x6<T>;
        <T extends object>(path: lodash.PropertyPath, object: T): T;
        (path: lodash.__, object: object): LodashSet2x6;
        <TResult>(path: lodash.PropertyPath, object: object): TResult;
    }
    interface LodashSet1x3 {
        <T extends object>(object: T): T;
        <TResult>(object: object): TResult;
    }
    interface LodashSet1x4<T> {
        (path: lodash.PropertyPath): LodashSet1x5<T>;
        (path: lodash.__, value: any): LodashSet1x6<T>;
        (path: lodash.PropertyPath, value: any): T;
    }
    type LodashSet1x5<T> = (value: any) => T;
    type LodashSet1x6<T> = (path: lodash.PropertyPath) => T;
    interface LodashSet2x4 {
        (path: lodash.PropertyPath): LodashSet2x5;
        (path: lodash.__, value: any): LodashSet2x6;
        <TResult>(path: lodash.PropertyPath, value: any): TResult;
    }
    type LodashSet2x5 = <TResult>(value: any) => TResult;
    type LodashSet2x6 = <TResult>(path: lodash.PropertyPath) => TResult;
    interface LodashAt {
        (props: lodash.PropertyPath): LodashAt1x1;
        <T>(props: lodash.__, object:  lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): LodashAt1x2<T>;
        <T>(props: lodash.PropertyPath, object:  lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): T[];
        <T extends object>(props: lodash.Many<keyof T>): LodashAt2x1<T>;
        <T extends object>(props: lodash.__, object: T | null | undefined): LodashAt2x2<T>;
        <T extends object>(props: lodash.Many<keyof T>, object: T | null | undefined): Array<T[keyof T]>;
    }
    type LodashAt1x1 = <T>(object:  lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined) => T[];
    type LodashAt1x2<T> = (props: lodash.PropertyPath) => T[];
    type LodashAt2x1<T> = (object: T | null | undefined) => Array<T[keyof T]>;
    type LodashAt2x2<T> = (props: lodash.Many<keyof T>) => Array<T[keyof T]>;
    type LodashAttempt = <TResult>(func: (...args: any[]) => TResult) => TResult | Error;
    interface LodashBefore {
        <TFunc extends (...args: any[]) => any>(func: TFunc): LodashBefore1x1<TFunc>;
        (func: lodash.__, n: number): LodashBefore1x2;
        <TFunc extends (...args: any[]) => any>(func: TFunc, n: number): TFunc;
    }
    type LodashBefore1x1<TFunc extends (...args: any[]) => any> = (n: number) => TFunc;
    type LodashBefore1x2 = <TFunc extends (...args: any[]) => any>(func: TFunc) => TFunc;
    interface LodashBind {
        (func: (...args: any[]) => any): LodashBind1x1;
        (func: lodash.__, thisArg: any): LodashBind1x2;
        (func: (...args: any[]) => any, thisArg: any): (...args: any[]) => any;
        placeholder: lodash.__;
    }
    type LodashBind1x1 = (thisArg: any) => (...args: any[]) => any;
    type LodashBind1x2 = (func: (...args: any[]) => any) => (...args: any[]) => any;
    interface LodashBindAll {
        (methodNames: lodash.Many<string>): LodashBindAll1x1;
        <T>(methodNames: lodash.__, object: T): LodashBindAll1x2<T>;
        <T>(methodNames: lodash.Many<string>, object: T): T;
    }
    type LodashBindAll1x1 = <T>(object: T) => T;
    type LodashBindAll1x2<T> = (methodNames: lodash.Many<string>) => T;
    interface LodashBindKey {
        (object: object): LodashBindKey1x1;
        (object: lodash.__, key: string): LodashBindKey1x2;
        (object: object, key: string): (...args: any[]) => any;
        placeholder: lodash.__;
    }
    type LodashBindKey1x1 = (key: string) => (...args: any[]) => any;
    type LodashBindKey1x2 = (object: object) => (...args: any[]) => any;
    type LodashCamelCase = (string: string) => string;
    type LodashCapitalize = (string: string) => string;
    type LodashCastArray = <T>(value: lodash.Many<T>) => T[];
    type LodashCeil = (n: number) => number;
    interface LodashChunk {
        (size: number): LodashChunk1x1;
        <T>(size: lodash.__, array: lodash.List<T> | null | undefined): LodashChunk1x2<T>;
        <T>(size: number, array: lodash.List<T> | null | undefined): T[][];
    }
    type LodashChunk1x1 = <T>(array: lodash.List<T> | null | undefined) => T[][];
    type LodashChunk1x2<T> = (size: number) => T[][];
    interface LodashClamp {
        (lower: number): LodashClamp1x1;
        (lower: lodash.__, upper: number): LodashClamp1x2;
        (lower: number, upper: number): LodashClamp1x3;
        (lower: lodash.__, upper: lodash.__, number: number): LodashClamp1x4;
        (lower: number, upper: lodash.__, number: number): LodashClamp1x5;
        (lower: lodash.__, upper: number, number: number): LodashClamp1x6;
        (lower: number, upper: number, number: number): number;
    }
    interface LodashClamp1x1 {
        (upper: number): LodashClamp1x3;
        (upper: lodash.__, number: number): LodashClamp1x5;
        (upper: number, number: number): number;
    }
    interface LodashClamp1x2 {
        (lower: number): LodashClamp1x3;
        (lower: lodash.__, number: number): LodashClamp1x6;
        (lower: number, number: number): number;
    }
    type LodashClamp1x3 = (number: number) => number;
    interface LodashClamp1x4 {
        (lower: number): LodashClamp1x5;
        (lower: lodash.__, upper: number): LodashClamp1x6;
        (lower: number, upper: number): number;
    }
    type LodashClamp1x5 = (upper: number) => number;
    type LodashClamp1x6 = (lower: number) => number;
    type LodashClone = <T>(value: T) => T;
    type LodashCloneDeep = <T>(value: T) => T;
    interface LodashCloneDeepWith {
        <T>(customizer: lodash.CloneDeepWithCustomizer<T>): LodashCloneDeepWith1x1<T>;
        <T>(customizer: lodash.__, value: T): LodashCloneDeepWith1x2<T>;
        <T>(customizer: lodash.CloneDeepWithCustomizer<T>, value: T): any;
    }
    type LodashCloneDeepWith1x1<T> = (value: T) => any;
    type LodashCloneDeepWith1x2<T> = (customizer: lodash.CloneDeepWithCustomizer<T>) => any;
    interface LodashCloneWith {
        <T, TResult extends object | string | number | boolean | null>(customizer: lodash.CloneWithCustomizer<T, TResult>): LodashCloneWith1x1<T, TResult>;
        <T>(customizer: lodash.__, value: T): LodashCloneWith1x2<T>;
        <T, TResult extends object | string | number | boolean | null>(customizer: lodash.CloneWithCustomizer<T, TResult>, value: T): TResult;
        <T, TResult>(customizer: lodash.CloneWithCustomizer<T, TResult | undefined>): LodashCloneWith2x1<T, TResult>;
        <T, TResult>(customizer: lodash.CloneWithCustomizer<T, TResult | undefined>, value: T): TResult | T;
    }
    type LodashCloneWith1x1<T, TResult> = (value: T) => TResult;
    interface LodashCloneWith1x2<T> {
        <TResult extends object | string | number | boolean | null>(customizer: lodash.CloneWithCustomizer<T, TResult>): TResult;
        <TResult>(customizer: lodash.CloneWithCustomizer<T, TResult | undefined>): TResult | T;
    }
    type LodashCloneWith2x1<T, TResult> = (value: T) => TResult | T;
    type LodashCompact = <T>(array: lodash.List<T> | null | undefined) => Array<Exclude<T, null | undefined | false | "" | 0>>;
    type LodashNegate = <T extends any[]>(predicate: (...args: T) => any) => (...args: T) => boolean;
    interface LodashFlowRight {
        <A extends any[], R1, R2, R3, R4, R5, R6, R7>(f7: (a: R6) => R7, f6: (a: R5) => R6, f5: (a: R4) => R5, f4: (a: R3) => R4, f3: (a: R2) => R3, f2: (a: R1) => R2, f1: (...args: A) => R1): (...args: A) => R7;
        <A extends any[], R1, R2, R3, R4, R5, R6>(f6: (a: R5) => R6, f5: (a: R4) => R5, f4: (a: R3) => R4, f3: (a: R2) => R3, f2: (a: R1) => R2, f1: (...args: A) => R1): (...args: A) => R6;
        <A extends any[], R1, R2, R3, R4, R5>(f5: (a: R4) => R5, f4: (a: R3) => R4, f3: (a: R2) => R3, f2: (a: R1) => R2, f1: (...args: A) => R1): (...args: A) => R5;
        <A extends any[], R1, R2, R3, R4>(f4: (a: R3) => R4, f3: (a: R2) => R3, f2: (a: R1) => R2, f1: (...args: A) => R1): (...args: A) => R4;
        <A extends any[], R1, R2, R3>(f3: (a: R2) => R3, f2: (a: R1) => R2, f1: (...args: A) => R1): (...args: A) => R3;
        <A extends any[], R1, R2>(f2: (a: R1) => R2, f1: (...args: A) => R1): (...args: A) => R2;
        (...func: Array<lodash.Many<(...args: any[]) => any>>): (...args: any[]) => any;
    }
    interface LodashConcat {
        <T>(array: lodash.Many<T>): LodashConcat1x1<T>;
        <T>(array: lodash.__, values: lodash.Many<T>): LodashConcat1x2<T>;
        <T>(array: lodash.Many<T>, values: lodash.Many<T>): T[];
    }
    type LodashConcat1x1<T> = (values: lodash.Many<T>) => T[];
    type LodashConcat1x2<T> = (array: lodash.Many<T>) => T[];
    type LodashCond = <T, R>(pairs: Array<lodash.CondPair<T, R>>) => (Target: T) => R;
    interface LodashConformsTo {
        <T>(source: lodash.ConformsPredicateObject<T>): LodashConformsTo1x1<T>;
        <T>(source: lodash.__, object: T): LodashConformsTo1x2<T>;
        <T>(source: lodash.ConformsPredicateObject<T>, object: T): boolean;
    }
    type LodashConformsTo1x1<T> = (object: T) => boolean;
    type LodashConformsTo1x2<T> = (source: lodash.ConformsPredicateObject<T>) => boolean;
    interface LodashContains {
        <T>(target: T): LodashContains1x1<T>;
        <T>(target: lodash.__, collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): LodashContains1x2<T>;
        <T>(target: T, collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): boolean;
    }
    type LodashContains1x1<T> = (collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined) => boolean;
    type LodashContains1x2<T> = (target: T) => boolean;
    interface LodashCountBy {
        <T>(iteratee: lodash.ValueIteratee<T>): LodashCountBy1x1<T>;
        <T>(iteratee: lodash.__, collection: lodash.List<T> | null | undefined): LodashCountBy1x2<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, collection: lodash.List<T> | null | undefined): lodash.Dictionary<number>;
        <T extends object>(iteratee: lodash.__, collection: T | null | undefined): LodashCountBy2x2<T>;
        <T extends object>(iteratee: lodash.ValueIteratee<T[keyof T]>, collection: T | null | undefined): lodash.Dictionary<number>;
    }
    type LodashCountBy1x1<T> = (collection: lodash.List<T> | object | null | undefined) => lodash.Dictionary<number>;
    type LodashCountBy1x2<T> = (iteratee: lodash.ValueIteratee<T>) => lodash.Dictionary<number>;
    type LodashCountBy2x2<T> = (iteratee: lodash.ValueIteratee<T[keyof T]>) => lodash.Dictionary<number>;
    type LodashCreate = <T extends object, U extends object>(prototype: T) => T & U;
    interface LodashCurry {
        <T1, R>(func: (t1: T1) => R): lodash.CurriedFunction1<T1, R>;
        <T1, T2, R>(func: (t1: T1, t2: T2) => R): lodash.CurriedFunction2<T1, T2, R>;
        <T1, T2, T3, R>(func: (t1: T1, t2: T2, t3: T3) => R): lodash.CurriedFunction3<T1, T2, T3, R>;
        <T1, T2, T3, T4, R>(func: (t1: T1, t2: T2, t3: T3, t4: T4) => R): lodash.CurriedFunction4<T1, T2, T3, T4, R>;
        <T1, T2, T3, T4, T5, R>(func: (t1: T1, t2: T2, t3: T3, t4: T4, t5: T5) => R): lodash.CurriedFunction5<T1, T2, T3, T4, T5, R>;
        (func: (...args: any[]) => any): (...args: any[]) => any;
        placeholder: lodash.__;
    }
    interface LodashCurryN {
        (arity: number): LodashCurryN1x1;
        <T1, R>(arity: lodash.__, func: (t1: T1) => R): LodashCurryN1x2<T1, R>;
        <T1, R>(arity: number, func: (t1: T1) => R): lodash.CurriedFunction1<T1, R>;
        <T1, T2, R>(arity: lodash.__, func: (t1: T1, t2: T2) => R): LodashCurryN2x2<T1, T2, R>;
        <T1, T2, R>(arity: number, func: (t1: T1, t2: T2) => R): lodash.CurriedFunction2<T1, T2, R>;
        <T1, T2, T3, R>(arity: lodash.__, func: (t1: T1, t2: T2, t3: T3) => R): LodashCurryN3x2<T1, T2, T3, R>;
        <T1, T2, T3, R>(arity: number, func: (t1: T1, t2: T2, t3: T3) => R): lodash.CurriedFunction3<T1, T2, T3, R>;
        <T1, T2, T3, T4, R>(arity: lodash.__, func: (t1: T1, t2: T2, t3: T3, t4: T4) => R): LodashCurryN4x2<T1, T2, T3, T4, R>;
        <T1, T2, T3, T4, R>(arity: number, func: (t1: T1, t2: T2, t3: T3, t4: T4) => R): lodash.CurriedFunction4<T1, T2, T3, T4, R>;
        <T1, T2, T3, T4, T5, R>(arity: lodash.__, func: (t1: T1, t2: T2, t3: T3, t4: T4, t5: T5) => R): LodashCurryN5x2<T1, T2, T3, T4, T5, R>;
        <T1, T2, T3, T4, T5, R>(arity: number, func: (t1: T1, t2: T2, t3: T3, t4: T4, t5: T5) => R): lodash.CurriedFunction5<T1, T2, T3, T4, T5, R>;
        (arity: lodash.__, func: (...args: any[]) => any): LodashCurryN6x2;
        (arity: number, func: (...args: any[]) => any): (...args: any[]) => any;
        placeholder: lodash.__;
    }
    interface LodashCurryN1x1 {
        <T1, R>(func: (t1: T1) => R): lodash.CurriedFunction1<T1, R>;
        <T1, T2, R>(func: (t1: T1, t2: T2) => R): lodash.CurriedFunction2<T1, T2, R>;
        <T1, T2, T3, R>(func: (t1: T1, t2: T2, t3: T3) => R): lodash.CurriedFunction3<T1, T2, T3, R>;
        <T1, T2, T3, T4, R>(func: (t1: T1, t2: T2, t3: T3, t4: T4) => R): lodash.CurriedFunction4<T1, T2, T3, T4, R>;
        <T1, T2, T3, T4, T5, R>(func: (t1: T1, t2: T2, t3: T3, t4: T4, t5: T5) => R): lodash.CurriedFunction5<T1, T2, T3, T4, T5, R>;
        (func: (...args: any[]) => any): (...args: any[]) => any;
    }
    type LodashCurryN1x2<T1, R> = (arity: number) => lodash.CurriedFunction1<T1, R>;
    type LodashCurryN2x2<T1, T2, R> = (arity: number) => lodash.CurriedFunction2<T1, T2, R>;
    type LodashCurryN3x2<T1, T2, T3, R> = (arity: number) => lodash.CurriedFunction3<T1, T2, T3, R>;
    type LodashCurryN4x2<T1, T2, T3, T4, R> = (arity: number) => lodash.CurriedFunction4<T1, T2, T3, T4, R>;
    type LodashCurryN5x2<T1, T2, T3, T4, T5, R> = (arity: number) => lodash.CurriedFunction5<T1, T2, T3, T4, T5, R>;
    type LodashCurryN6x2 = (arity: number) => (...args: any[]) => any;
    interface LodashCurryRight {
        <T1, R>(func: (t1: T1) => R): lodash.RightCurriedFunction1<T1, R>;
        <T1, T2, R>(func: (t1: T1, t2: T2) => R): lodash.RightCurriedFunction2<T1, T2, R>;
        <T1, T2, T3, R>(func: (t1: T1, t2: T2, t3: T3) => R): lodash.RightCurriedFunction3<T1, T2, T3, R>;
        <T1, T2, T3, T4, R>(func: (t1: T1, t2: T2, t3: T3, t4: T4) => R): lodash.RightCurriedFunction4<T1, T2, T3, T4, R>;
        <T1, T2, T3, T4, T5, R>(func: (t1: T1, t2: T2, t3: T3, t4: T4, t5: T5) => R): lodash.RightCurriedFunction5<T1, T2, T3, T4, T5, R>;
        (func: (...args: any[]) => any): (...args: any[]) => any;
        placeholder: lodash.__;
    }
    interface LodashCurryRightN {
        (arity: number): LodashCurryRightN1x1;
        <T1, R>(arity: lodash.__, func: (t1: T1) => R): LodashCurryRightN1x2<T1, R>;
        <T1, R>(arity: number, func: (t1: T1) => R): lodash.RightCurriedFunction1<T1, R>;
        <T1, T2, R>(arity: lodash.__, func: (t1: T1, t2: T2) => R): LodashCurryRightN2x2<T1, T2, R>;
        <T1, T2, R>(arity: number, func: (t1: T1, t2: T2) => R): lodash.RightCurriedFunction2<T1, T2, R>;
        <T1, T2, T3, R>(arity: lodash.__, func: (t1: T1, t2: T2, t3: T3) => R): LodashCurryRightN3x2<T1, T2, T3, R>;
        <T1, T2, T3, R>(arity: number, func: (t1: T1, t2: T2, t3: T3) => R): lodash.RightCurriedFunction3<T1, T2, T3, R>;
        <T1, T2, T3, T4, R>(arity: lodash.__, func: (t1: T1, t2: T2, t3: T3, t4: T4) => R): LodashCurryRightN4x2<T1, T2, T3, T4, R>;
        <T1, T2, T3, T4, R>(arity: number, func: (t1: T1, t2: T2, t3: T3, t4: T4) => R): lodash.RightCurriedFunction4<T1, T2, T3, T4, R>;
        <T1, T2, T3, T4, T5, R>(arity: lodash.__, func: (t1: T1, t2: T2, t3: T3, t4: T4, t5: T5) => R): LodashCurryRightN5x2<T1, T2, T3, T4, T5, R>;
        <T1, T2, T3, T4, T5, R>(arity: number, func: (t1: T1, t2: T2, t3: T3, t4: T4, t5: T5) => R): lodash.RightCurriedFunction5<T1, T2, T3, T4, T5, R>;
        (arity: lodash.__, func: (...args: any[]) => any): LodashCurryRightN6x2;
        (arity: number, func: (...args: any[]) => any): (...args: any[]) => any;
        placeholder: lodash.__;
    }
    interface LodashCurryRightN1x1 {
        <T1, R>(func: (t1: T1) => R): lodash.RightCurriedFunction1<T1, R>;
        <T1, T2, R>(func: (t1: T1, t2: T2) => R): lodash.RightCurriedFunction2<T1, T2, R>;
        <T1, T2, T3, R>(func: (t1: T1, t2: T2, t3: T3) => R): lodash.RightCurriedFunction3<T1, T2, T3, R>;
        <T1, T2, T3, T4, R>(func: (t1: T1, t2: T2, t3: T3, t4: T4) => R): lodash.RightCurriedFunction4<T1, T2, T3, T4, R>;
        <T1, T2, T3, T4, T5, R>(func: (t1: T1, t2: T2, t3: T3, t4: T4, t5: T5) => R): lodash.RightCurriedFunction5<T1, T2, T3, T4, T5, R>;
        (func: (...args: any[]) => any): (...args: any[]) => any;
    }
    type LodashCurryRightN1x2<T1, R> = (arity: number) => lodash.RightCurriedFunction1<T1, R>;
    type LodashCurryRightN2x2<T1, T2, R> = (arity: number) => lodash.RightCurriedFunction2<T1, T2, R>;
    type LodashCurryRightN3x2<T1, T2, T3, R> = (arity: number) => lodash.RightCurriedFunction3<T1, T2, T3, R>;
    type LodashCurryRightN4x2<T1, T2, T3, T4, R> = (arity: number) => lodash.RightCurriedFunction4<T1, T2, T3, T4, R>;
    type LodashCurryRightN5x2<T1, T2, T3, T4, T5, R> = (arity: number) => lodash.RightCurriedFunction5<T1, T2, T3, T4, T5, R>;
    type LodashCurryRightN6x2 = (arity: number) => (...args: any[]) => any;
    interface LodashDebounce {
        (wait: number): LodashDebounce1x1;
        <T extends (...args: any) => any>(wait: lodash.__, func: T): LodashDebounce1x2<T>;
        <T extends (...args: any) => any>(wait: number, func: T): lodash.DebouncedFunc<T>;
    }
    type LodashDebounce1x1 = <T extends (...args: any) => any>(func: T) => lodash.DebouncedFunc<T>;
    type LodashDebounce1x2<T extends (...args: any) => any> = (wait: number) => lodash.DebouncedFunc<T>;
    type LodashDeburr = (string: string) => string;
    interface LodashDefaults {
        <TSource>(source: TSource): LodashDefaults1x1<TSource>;
        <TObject>(source: lodash.__, object: TObject): LodashDefaults1x2<TObject>;
        <TObject, TSource>(source: TSource, object: TObject): TSource & TObject;
    }
    type LodashDefaults1x1<TSource> = <TObject>(object: TObject) => TSource & TObject;
    type LodashDefaults1x2<TObject> = <TSource>(source: TSource) => TSource & TObject;
    interface LodashDefaultsAll {
        <TObject, TSource>(object: [TObject, TSource]): TSource & TObject;
        <TObject, TSource1, TSource2>(object: [TObject, TSource1, TSource2]): TSource2 & TSource1 & TObject;
        <TObject, TSource1, TSource2, TSource3>(object: [TObject, TSource1, TSource2, TSource3]): TSource3 & TSource2 & TSource1 & TObject;
        <TObject, TSource1, TSource2, TSource3, TSource4>(object: [TObject, TSource1, TSource2, TSource3, TSource4]): TSource4 & TSource3 & TSource2 & TSource1 & TObject;
        <TObject>(object: [TObject]): TObject;
        (object: ReadonlyArray<any>): any;
    }
    interface LodashDefaultsDeep {
        (sources: any): LodashDefaultsDeep1x1;
        (sources: lodash.__, object: any): LodashDefaultsDeep1x2;
        (sources: any, object: any): any;
    }
    type LodashDefaultsDeep1x1 = (object: any) => any;
    type LodashDefaultsDeep1x2 = (sources: any) => any;
    type LodashDefaultsDeepAll = (object: ReadonlyArray<any>) => any;
    interface LodashDefaultTo {
        <T>(defaultValue: T): LodashDefaultTo1x1<T>;
        <T>(defaultValue: lodash.__, value: T | null | undefined): LodashDefaultTo1x2<T>;
        <T>(defaultValue: T, value: T | null | undefined): T;
        <TDefault>(defaultValue: TDefault): LodashDefaultTo2x1<TDefault>;
        <T, TDefault>(defaultValue: TDefault, value: T | null | undefined): T | TDefault;
    }
    type LodashDefaultTo1x1<T> = (value: T | null | undefined) => T;
    interface LodashDefaultTo1x2<T> {
        (defaultValue: T): T;
        <TDefault>(defaultValue: TDefault): T | TDefault;
    }
    type LodashDefaultTo2x1<TDefault> = <T>(value: T | null | undefined) => T | TDefault;
    type LodashDefer = (func: (...args: any[]) => any, ...args: any[]) => number;
    interface LodashDelay {
        (wait: number): LodashDelay1x1;
        (wait: lodash.__, func: (...args: any[]) => any): LodashDelay1x2;
        (wait: number, func: (...args: any[]) => any): number;
    }
    type LodashDelay1x1 = (func: (...args: any[]) => any) => number;
    type LodashDelay1x2 = (wait: number) => number;
    interface LodashDifference {
        <T>(array: lodash.List<T> | null | undefined): LodashDifference1x1<T>;
        <T>(array: lodash.__, values: lodash.List<T>): LodashDifference1x2<T>;
        <T>(array: lodash.List<T> | null | undefined, values: lodash.List<T>): T[];
    }
    type LodashDifference1x1<T> = (values: lodash.List<T>) => T[];
    type LodashDifference1x2<T> = (array: lodash.List<T> | null | undefined) => T[];
    interface LodashDifferenceBy {
        <T1, T2>(iteratee: lodash.ValueIteratee<T1 | T2>): LodashDifferenceBy1x1<T1, T2>;
        <T1>(iteratee: lodash.__, array: lodash.List<T1> | null | undefined): LodashDifferenceBy1x2<T1>;
        <T1, T2>(iteratee: lodash.ValueIteratee<T1 | T2>, array: lodash.List<T1> | null | undefined): LodashDifferenceBy1x3<T1, T2>;
        <T2>(iteratee: lodash.__, array: lodash.__, values: lodash.List<T2>): LodashDifferenceBy1x4<T2>;
        <T1, T2>(iteratee: lodash.ValueIteratee<T1 | T2>, array: lodash.__, values: lodash.List<T2>): LodashDifferenceBy1x5<T1>;
        <T1, T2>(iteratee: lodash.__, array: lodash.List<T1> | null | undefined, values: lodash.List<T2>): LodashDifferenceBy1x6<T1, T2>;
        <T1, T2>(iteratee: lodash.ValueIteratee<T1 | T2>, array: lodash.List<T1> | null | undefined, values: lodash.List<T2>): T1[];
    }
    interface LodashDifferenceBy1x1<T1, T2> {
        (array: lodash.List<T1> | null | undefined): LodashDifferenceBy1x3<T1, T2>;
        (array: lodash.__, values: lodash.List<T2>): LodashDifferenceBy1x5<T1>;
        (array: lodash.List<T1> | null | undefined, values: lodash.List<T2>): T1[];
    }
    interface LodashDifferenceBy1x2<T1> {
        <T2>(iteratee: lodash.ValueIteratee<T1 | T2>): LodashDifferenceBy1x3<T1, T2>;
        <T2>(iteratee: lodash.__, values: lodash.List<T2>): LodashDifferenceBy1x6<T1, T2>;
        <T2>(iteratee: lodash.ValueIteratee<T1 | T2>, values: lodash.List<T2>): T1[];
    }
    type LodashDifferenceBy1x3<T1, T2> = (values: lodash.List<T2>) => T1[];
    interface LodashDifferenceBy1x4<T2> {
        <T1>(iteratee: lodash.ValueIteratee<T1 | T2>): LodashDifferenceBy1x5<T1>;
        <T1>(iteratee: lodash.__, array: lodash.List<T1> | null | undefined): LodashDifferenceBy1x6<T1, T2>;
        <T1>(iteratee: lodash.ValueIteratee<T1 | T2>, array: lodash.List<T1> | null | undefined): T1[];
    }
    type LodashDifferenceBy1x5<T1> = (array: lodash.List<T1> | null | undefined) => T1[];
    type LodashDifferenceBy1x6<T1, T2> = (iteratee: lodash.ValueIteratee<T1 | T2>) => T1[];
    interface LodashDifferenceWith {
        <T1, T2>(comparator: lodash.Comparator2<T1, T2>): LodashDifferenceWith1x1<T1, T2>;
        <T1>(comparator: lodash.__, array: lodash.List<T1> | null | undefined): LodashDifferenceWith1x2<T1>;
        <T1, T2>(comparator: lodash.Comparator2<T1, T2>, array: lodash.List<T1> | null | undefined): LodashDifferenceWith1x3<T1, T2>;
        <T2>(comparator: lodash.__, array: lodash.__, values: lodash.List<T2>): LodashDifferenceWith1x4<T2>;
        <T1, T2>(comparator: lodash.Comparator2<T1, T2>, array: lodash.__, values: lodash.List<T2>): LodashDifferenceWith1x5<T1>;
        <T1, T2>(comparator: lodash.__, array: lodash.List<T1> | null | undefined, values: lodash.List<T2>): LodashDifferenceWith1x6<T1, T2>;
        <T1, T2>(comparator: lodash.Comparator2<T1, T2>, array: lodash.List<T1> | null | undefined, values: lodash.List<T2>): T1[];
    }
    interface LodashDifferenceWith1x1<T1, T2> {
        (array: lodash.List<T1> | null | undefined): LodashDifferenceWith1x3<T1, T2>;
        (array: lodash.__, values: lodash.List<T2>): LodashDifferenceWith1x5<T1>;
        (array: lodash.List<T1> | null | undefined, values: lodash.List<T2>): T1[];
    }
    interface LodashDifferenceWith1x2<T1> {
        <T2>(comparator: lodash.Comparator2<T1, T2>): LodashDifferenceWith1x3<T1, T2>;
        <T2>(comparator: lodash.__, values: lodash.List<T2>): LodashDifferenceWith1x6<T1, T2>;
        <T2>(comparator: lodash.Comparator2<T1, T2>, values: lodash.List<T2>): T1[];
    }
    type LodashDifferenceWith1x3<T1, T2> = (values: lodash.List<T2>) => T1[];
    interface LodashDifferenceWith1x4<T2> {
        <T1>(comparator: lodash.Comparator2<T1, T2>): LodashDifferenceWith1x5<T1>;
        <T1>(comparator: lodash.__, array: lodash.List<T1> | null | undefined): LodashDifferenceWith1x6<T1, T2>;
        <T1>(comparator: lodash.Comparator2<T1, T2>, array: lodash.List<T1> | null | undefined): T1[];
    }
    type LodashDifferenceWith1x5<T1> = (array: lodash.List<T1> | null | undefined) => T1[];
    type LodashDifferenceWith1x6<T1, T2> = (comparator: lodash.Comparator2<T1, T2>) => T1[];
    interface LodashUnset {
        (path: lodash.PropertyPath): LodashUnset1x1;
        <T>(path: lodash.__, object: T): LodashUnset1x2<T>;
        <T>(path: lodash.PropertyPath, object: T): T;
    }
    type LodashUnset1x1 = <T>(object: T) => T;
    type LodashUnset1x2<T> = (path: lodash.PropertyPath) => T;
    interface LodashDivide {
        (dividend: number): LodashDivide1x1;
        (dividend: lodash.__, divisor: number): LodashDivide1x2;
        (dividend: number, divisor: number): number;
    }
    type LodashDivide1x1 = (divisor: number) => number;
    type LodashDivide1x2 = (dividend: number) => number;
    interface LodashDrop {
        (n: number): LodashDrop1x1;
        <T>(n: lodash.__, array: lodash.List<T> | null | undefined): LodashDrop1x2<T>;
        <T>(n: number, array: lodash.List<T> | null | undefined): T[];
    }
    type LodashDrop1x1 = <T>(array: lodash.List<T> | null | undefined) => T[];
    type LodashDrop1x2<T> = (n: number) => T[];
    interface LodashDropRight {
        (n: number): LodashDropRight1x1;
        <T>(n: lodash.__, array: lodash.List<T> | null | undefined): LodashDropRight1x2<T>;
        <T>(n: number, array: lodash.List<T> | null | undefined): T[];
    }
    type LodashDropRight1x1 = <T>(array: lodash.List<T> | null | undefined) => T[];
    type LodashDropRight1x2<T> = (n: number) => T[];
    interface LodashDropRightWhile {
        <T>(predicate: lodash.ValueIteratee<T>): LodashDropRightWhile1x1<T>;
        <T>(predicate: lodash.__, array: lodash.List<T> | null | undefined): LodashDropRightWhile1x2<T>;
        <T>(predicate: lodash.ValueIteratee<T>, array: lodash.List<T> | null | undefined): T[];
    }
    type LodashDropRightWhile1x1<T> = (array: lodash.List<T> | null | undefined) => T[];
    type LodashDropRightWhile1x2<T> = (predicate: lodash.ValueIteratee<T>) => T[];
    interface LodashDropWhile {
        <T>(predicate: lodash.ValueIteratee<T>): LodashDropWhile1x1<T>;
        <T>(predicate: lodash.__, array: lodash.List<T> | null | undefined): LodashDropWhile1x2<T>;
        <T>(predicate: lodash.ValueIteratee<T>, array: lodash.List<T> | null | undefined): T[];
    }
    type LodashDropWhile1x1<T> = (array: lodash.List<T> | null | undefined) => T[];
    type LodashDropWhile1x2<T> = (predicate: lodash.ValueIteratee<T>) => T[];
    interface LodashForEach {
        <T>(iteratee: (value: T) => any): LodashForEach1x1<T>;
        <T>(iteratee: lodash.__, collection: ReadonlyArray<T>): LodashForEach1x2<T>;
        <T>(iteratee: (value: T) => any, collection: ReadonlyArray<T>): T[];
        <T>(iteratee: lodash.__, collection: lodash.List<T>): LodashForEach2x2<T>;
        <T>(iteratee: (value: T) => any, collection: lodash.List<T>): lodash.List<T>;
        <T extends object>(iteratee: lodash.__, collection: T): LodashForEach3x2<T>;
        <T extends object>(iteratee: (value: T[keyof T]) => any, collection: T): T;
        <T, TArray extends T[] | null | undefined>(iteratee: lodash.__, collection: TArray & (T[] | null | undefined)): LodashForEach4x2<T, TArray>;
        <T, TArray extends T[] | null | undefined>(iteratee: (value: T) => any, collection: TArray & (T[] | null | undefined)): TArray;
        <T, TList extends lodash.List<T> | null | undefined>(iteratee: lodash.__, collection: TList & (lodash.List<T> | null | undefined)): LodashForEach5x2<T, TList>;
        <T, TList extends lodash.List<T> | null | undefined>(iteratee: (value: T) => any, collection: TList & (lodash.List<T> | null | undefined)): TList;
        <T extends object>(iteratee: lodash.__, collection: T | null | undefined): LodashForEach6x2<T>;
        <T extends object>(iteratee: (value: T[keyof T]) => any, collection: T | null | undefined): T | null | undefined;
    }
    interface LodashForEach1x1<T> {
        (collection: ReadonlyArray<T>): T[];
        (collection: lodash.List<T>): lodash.List<T>;
        <T1 extends object>(collection: T1): T1;
        <TArray extends T[] | null | undefined>(collection: TArray & (T[] | null | undefined)): TArray;
        <TList extends lodash.List<T> | null | undefined>(collection: TList & (lodash.List<T> | null | undefined)): TList;
        <T1 extends object>(collection: T1 | null | undefined): T1 | null | undefined;
    }
    type LodashForEach1x2<T> = (iteratee: (value: T) => any) => T[];
    type LodashForEach2x2<T> = (iteratee: (value: T) => any) => lodash.List<T>;
    type LodashForEach3x2<T> = (iteratee: (value: T[keyof T]) => any) => T;
    type LodashForEach4x2<T, TArray> = (iteratee: (value: T) => any) => TArray;
    type LodashForEach5x2<T, TList> = (iteratee: (value: T) => any) => TList;
    type LodashForEach6x2<T> = (iteratee: (value: T[keyof T]) => any) => T | null | undefined;
    interface LodashForEachRight {
        <T>(iteratee: (value: T) => any): LodashForEachRight1x1<T>;
        <T>(iteratee: lodash.__, collection: ReadonlyArray<T>): LodashForEachRight1x2<T>;
        <T>(iteratee: (value: T) => any, collection: ReadonlyArray<T>): T[];
        <T>(iteratee: lodash.__, collection: lodash.List<T>): LodashForEachRight2x2<T>;
        <T>(iteratee: (value: T) => any, collection: lodash.List<T>): lodash.List<T>;
        <T extends object>(iteratee: lodash.__, collection: T): LodashForEachRight3x2<T>;
        <T extends object>(iteratee: (value: T[keyof T]) => any, collection: T): T;
        <T, TArray extends T[] | null | undefined>(iteratee: lodash.__, collection: TArray & (T[] | null | undefined)): LodashForEachRight4x2<T, TArray>;
        <T, TArray extends T[] | null | undefined>(iteratee: (value: T) => any, collection: TArray & (T[] | null | undefined)): TArray;
        <T, TList extends lodash.List<T> | null | undefined>(iteratee: lodash.__, collection: TList & (lodash.List<T> | null | undefined)): LodashForEachRight5x2<T, TList>;
        <T, TList extends lodash.List<T> | null | undefined>(iteratee: (value: T) => any, collection: TList & (lodash.List<T> | null | undefined)): TList;
        <T extends object>(iteratee: lodash.__, collection: T | null | undefined): LodashForEachRight6x2<T>;
        <T extends object>(iteratee: (value: T[keyof T]) => any, collection: T | null | undefined): T | null | undefined;
    }
    interface LodashForEachRight1x1<T> {
        (collection: ReadonlyArray<T>): T[];
        (collection: lodash.List<T>): lodash.List<T>;
        <T1 extends object>(collection: T1): T1;
        <TArray extends T[] | null | undefined>(collection: TArray & (T[] | null | undefined)): TArray;
        <TList extends lodash.List<T> | null | undefined>(collection: TList & (lodash.List<T> | null | undefined)): TList;
        <T1 extends object>(collection: T1 | null | undefined): T1 | null | undefined;
    }
    type LodashForEachRight1x2<T> = (iteratee: (value: T) => any) => T[];
    type LodashForEachRight2x2<T> = (iteratee: (value: T) => any) => lodash.List<T>;
    type LodashForEachRight3x2<T> = (iteratee: (value: T[keyof T]) => any) => T;
    type LodashForEachRight4x2<T, TArray> = (iteratee: (value: T) => any) => TArray;
    type LodashForEachRight5x2<T, TList> = (iteratee: (value: T) => any) => TList;
    type LodashForEachRight6x2<T> = (iteratee: (value: T[keyof T]) => any) => T | null | undefined;
    interface LodashEndsWith {
        (target: string): LodashEndsWith1x1;
        (target: lodash.__, string: string): LodashEndsWith1x2;
        (target: string, string: string): boolean;
    }
    type LodashEndsWith1x1 = (string: string) => boolean;
    type LodashEndsWith1x2 = (target: string) => boolean;
    interface LodashToPairs {
        <T>(object: lodash.Dictionary<T> | lodash.NumericDictionary<T>): Array<[string, T]>;
        (object: object): Array<[string, any]>;
    }
    interface LodashToPairsIn {
        <T>(object: lodash.Dictionary<T> | lodash.NumericDictionary<T>): Array<[string, T]>;
        (object: object): Array<[string, any]>;
    }
    interface LodashEq {
        (value: any): LodashEq1x1;
        (value: lodash.__, other: any): LodashEq1x2;
        (value: any, other: any): boolean;
    }
    type LodashEq1x1 = (other: any) => boolean;
    type LodashEq1x2 = (value: any) => boolean;
    interface LodashIsEqual {
        (value: any): LodashIsEqual1x1;
        (value: lodash.__, other: any): LodashIsEqual1x2;
        (value: any, other: any): boolean;
    }
    type LodashIsEqual1x1 = (other: any) => boolean;
    type LodashIsEqual1x2 = (value: any) => boolean;
    type LodashEscape = (string: string) => string;
    type LodashEscapeRegExp = (string: string) => string;
    interface LodashExtend {
        <TObject>(object: TObject): LodashExtend1x1<TObject>;
        <TSource>(object: lodash.__, source: TSource): LodashExtend1x2<TSource>;
        <TObject, TSource>(object: TObject, source: TSource): TObject & TSource;
    }
    type LodashExtend1x1<TObject> = <TSource>(source: TSource) => TObject & TSource;
    type LodashExtend1x2<TSource> = <TObject>(object: TObject) => TObject & TSource;
    interface LodashExtendAll {
        <TObject, TSource>(object: [TObject, TSource]): TObject & TSource;
        <TObject, TSource1, TSource2>(object: [TObject, TSource1, TSource2]): TObject & TSource1 & TSource2;
        <TObject, TSource1, TSource2, TSource3>(object: [TObject, TSource1, TSource2, TSource3]): TObject & TSource1 & TSource2 & TSource3;
        <TObject, TSource1, TSource2, TSource3, TSource4>(object: [TObject, TSource1, TSource2, TSource3, TSource4]): TObject & TSource1 & TSource2 & TSource3 & TSource4;
        <TObject>(object: [TObject]): TObject;
        <TResult>(object: ReadonlyArray<any>): TResult;
    }
    interface LodashExtendAllWith {
        (customizer: lodash.AssignCustomizer): LodashExtendAllWith1x1;
        (customizer: lodash.__, args: ReadonlyArray<any>): LodashExtendAllWith1x2;
        (customizer: lodash.AssignCustomizer, args: ReadonlyArray<any>): any;
    }
    type LodashExtendAllWith1x1 = (args: ReadonlyArray<any>) => any;
    type LodashExtendAllWith1x2 = (customizer: lodash.AssignCustomizer) => any;
    interface LodashExtendWith {
        (customizer: lodash.AssignCustomizer): LodashExtendWith1x1;
        <TObject>(customizer: lodash.__, object: TObject): LodashExtendWith1x2<TObject>;
        <TObject>(customizer: lodash.AssignCustomizer, object: TObject): LodashExtendWith1x3<TObject>;
        <TSource>(customizer: lodash.__, object: lodash.__, source: TSource): LodashExtendWith1x4<TSource>;
        <TSource>(customizer: lodash.AssignCustomizer, object: lodash.__, source: TSource): LodashExtendWith1x5<TSource>;
        <TObject, TSource>(customizer: lodash.__, object: TObject, source: TSource): LodashExtendWith1x6<TObject, TSource>;
        <TObject, TSource>(customizer: lodash.AssignCustomizer, object: TObject, source: TSource): TObject & TSource;
    }
    interface LodashExtendWith1x1 {
        <TObject>(object: TObject): LodashExtendWith1x3<TObject>;
        <TSource>(object: lodash.__, source: TSource): LodashExtendWith1x5<TSource>;
        <TObject, TSource>(object: TObject, source: TSource): TObject & TSource;
    }
    interface LodashExtendWith1x2<TObject> {
        (customizer: lodash.AssignCustomizer): LodashExtendWith1x3<TObject>;
        <TSource>(customizer: lodash.__, source: TSource): LodashExtendWith1x6<TObject, TSource>;
        <TSource>(customizer: lodash.AssignCustomizer, source: TSource): TObject & TSource;
    }
    type LodashExtendWith1x3<TObject> = <TSource>(source: TSource) => TObject & TSource;
    interface LodashExtendWith1x4<TSource> {
        (customizer: lodash.AssignCustomizer): LodashExtendWith1x5<TSource>;
        <TObject>(customizer: lodash.__, object: TObject): LodashExtendWith1x6<TObject, TSource>;
        <TObject>(customizer: lodash.AssignCustomizer, object: TObject): TObject & TSource;
    }
    type LodashExtendWith1x5<TSource> = <TObject>(object: TObject) => TObject & TSource;
    type LodashExtendWith1x6<TObject, TSource> = (customizer: lodash.AssignCustomizer) => TObject & TSource;
    type LodashStubFalse = () => false;
    interface LodashFill {
        (start: number): LodashFill1x1;
        (start: lodash.__, end: number): LodashFill1x2;
        (start: number, end: number): LodashFill1x3;
        <T>(start: lodash.__, end: lodash.__, value: T): LodashFill1x4<T>;
        <T>(start: number, end: lodash.__, value: T): LodashFill1x5<T>;
        <T>(start: lodash.__, end: number, value: T): LodashFill1x6<T>;
        <T>(start: number, end: number, value: T): LodashFill1x7<T>;
        <U>(start: lodash.__, end: lodash.__, value: lodash.__, array: U[] | null | undefined): LodashFill1x8<U>;
        <U>(start: number, end: lodash.__, value: lodash.__, array: U[] | null | undefined): LodashFill1x9<U>;
        <U>(start: lodash.__, end: number, value: lodash.__, array: U[] | null | undefined): LodashFill1x10<U>;
        <U>(start: number, end: number, value: lodash.__, array: U[] | null | undefined): LodashFill1x11<U>;
        <T, U>(start: lodash.__, end: lodash.__, value: T, array: U[] | null | undefined): LodashFill1x12<T, U>;
        <T, U>(start: number, end: lodash.__, value: T, array: U[] | null | undefined): LodashFill1x13<T, U>;
        <T, U>(start: lodash.__, end: number, value: T, array: U[] | null | undefined): LodashFill1x14<T, U>;
        <T, U>(start: number, end: number, value: T, array: U[] | null | undefined): Array<T | U>;
        <U>(start: lodash.__, end: lodash.__, value: lodash.__, array: lodash.List<U> | null | undefined): LodashFill2x8<U>;
        <U>(start: number, end: lodash.__, value: lodash.__, array: lodash.List<U> | null | undefined): LodashFill2x9<U>;
        <U>(start: lodash.__, end: number, value: lodash.__, array: lodash.List<U> | null | undefined): LodashFill2x10<U>;
        <U>(start: number, end: number, value: lodash.__, array: lodash.List<U> | null | undefined): LodashFill2x11<U>;
        <T, U>(start: lodash.__, end: lodash.__, value: T, array: lodash.List<U> | null | undefined): LodashFill2x12<T, U>;
        <T, U>(start: number, end: lodash.__, value: T, array: lodash.List<U> | null | undefined): LodashFill2x13<T, U>;
        <T, U>(start: lodash.__, end: number, value: T, array: lodash.List<U> | null | undefined): LodashFill2x14<T, U>;
        <T, U>(start: number, end: number, value: T, array: lodash.List<U> | null | undefined): lodash.List<T | U>;
    }
    interface LodashFill1x1 {
        (end: number): LodashFill1x3;
        <T>(end: lodash.__, value: T): LodashFill1x5<T>;
        <T>(end: number, value: T): LodashFill1x7<T>;
        <U>(end: lodash.__, value: lodash.__, array: U[] | null | undefined): LodashFill1x9<U>;
        <U>(end: number, value: lodash.__, array: U[] | null | undefined): LodashFill1x11<U>;
        <T, U>(end: lodash.__, value: T, array: U[] | null | undefined): LodashFill1x13<T, U>;
        <T, U>(end: number, value: T, array: U[] | null | undefined): Array<T | U>;
        <U>(end: lodash.__, value: lodash.__, array: lodash.List<U> | null | undefined): LodashFill2x9<U>;
        <U>(end: number, value: lodash.__, array: lodash.List<U> | null | undefined): LodashFill2x11<U>;
        <T, U>(end: lodash.__, value: T, array: lodash.List<U> | null | undefined): LodashFill2x13<T, U>;
        <T, U>(end: number, value: T, array: lodash.List<U> | null | undefined): lodash.List<T | U>;
    }
    interface LodashFill1x2 {
        (start: number): LodashFill1x3;
        <T>(start: lodash.__, value: T): LodashFill1x6<T>;
        <T>(start: number, value: T): LodashFill1x7<T>;
        <U>(start: lodash.__, value: lodash.__, array: U[] | null | undefined): LodashFill1x10<U>;
        <U>(start: number, value: lodash.__, array: U[] | null | undefined): LodashFill1x11<U>;
        <T, U>(start: lodash.__, value: T, array: U[] | null | undefined): LodashFill1x14<T, U>;
        <T, U>(start: number, value: T, array: U[] | null | undefined): Array<T | U>;
        <U>(start: lodash.__, value: lodash.__, array: lodash.List<U> | null | undefined): LodashFill2x10<U>;
        <U>(start: number, value: lodash.__, array: lodash.List<U> | null | undefined): LodashFill2x11<U>;
        <T, U>(start: lodash.__, value: T, array: lodash.List<U> | null | undefined): LodashFill2x14<T, U>;
        <T, U>(start: number, value: T, array: lodash.List<U> | null | undefined): lodash.List<T | U>;
    }
    interface LodashFill1x3 {
        <T>(value: T): LodashFill1x7<T>;
        <U>(value: lodash.__, array: U[] | null | undefined): LodashFill1x11<U>;
        <T, U>(value: T, array: U[] | null | undefined): Array<T | U>;
        <U>(value: lodash.__, array: lodash.List<U> | null | undefined): LodashFill2x11<U>;
        <T, U>(value: T, array: lodash.List<U> | null | undefined): lodash.List<T | U>;
    }
    interface LodashFill1x4<T> {
        (start: number): LodashFill1x5<T>;
        (start: lodash.__, end: number): LodashFill1x6<T>;
        (start: number, end: number): LodashFill1x7<T>;
        <U>(start: lodash.__, end: lodash.__, array: U[] | null | undefined): LodashFill1x12<T, U>;
        <U>(start: number, end: lodash.__, array: U[] | null | undefined): LodashFill1x13<T, U>;
        <U>(start: lodash.__, end: number, array: U[] | null | undefined): LodashFill1x14<T, U>;
        <U>(start: number, end: number, array: U[] | null | undefined): Array<T | U>;
        <U>(start: lodash.__, end: lodash.__, array: lodash.List<U> | null | undefined): LodashFill2x12<T, U>;
        <U>(start: number, end: lodash.__, array: lodash.List<U> | null | undefined): LodashFill2x13<T, U>;
        <U>(start: lodash.__, end: number, array: lodash.List<U> | null | undefined): LodashFill2x14<T, U>;
        <U>(start: number, end: number, array: lodash.List<U> | null | undefined): lodash.List<T | U>;
    }
    interface LodashFill1x5<T> {
        (end: number): LodashFill1x7<T>;
        <U>(end: lodash.__, array: U[] | null | undefined): LodashFill1x13<T, U>;
        <U>(end: number, array: U[] | null | undefined): Array<T | U>;
        <U>(end: lodash.__, array: lodash.List<U> | null | undefined): LodashFill2x13<T, U>;
        <U>(end: number, array: lodash.List<U> | null | undefined): lodash.List<T | U>;
    }
    interface LodashFill1x6<T> {
        (start: number): LodashFill1x7<T>;
        <U>(start: lodash.__, array: U[] | null | undefined): LodashFill1x14<T, U>;
        <U>(start: number, array: U[] | null | undefined): Array<T | U>;
        <U>(start: lodash.__, array: lodash.List<U> | null | undefined): LodashFill2x14<T, U>;
        <U>(start: number, array: lodash.List<U> | null | undefined): lodash.List<T | U>;
    }
    interface LodashFill1x7<T> {
        <U>(array: U[] | null | undefined): Array<T | U>;
        <U>(array: lodash.List<U> | null | undefined): lodash.List<T | U>;
    }
    interface LodashFill1x8<U> {
        (start: number): LodashFill1x9<U>;
        (start: lodash.__, end: number): LodashFill1x10<U>;
        (start: number, end: number): LodashFill1x11<U>;
        <T>(start: lodash.__, end: lodash.__, value: T): LodashFill1x12<T, U>;
        <T>(start: number, end: lodash.__, value: T): LodashFill1x13<T, U>;
        <T>(start: lodash.__, end: number, value: T): LodashFill1x14<T, U>;
        <T>(start: number, end: number, value: T): Array<T | U>;
    }
    interface LodashFill1x9<U> {
        (end: number): LodashFill1x11<U>;
        <T>(end: lodash.__, value: T): LodashFill1x13<T, U>;
        <T>(end: number, value: T): Array<T | U>;
    }
    interface LodashFill1x10<U> {
        (start: number): LodashFill1x11<U>;
        <T>(start: lodash.__, value: T): LodashFill1x14<T, U>;
        <T>(start: number, value: T): Array<T | U>;
    }
    type LodashFill1x11<U> = <T>(value: T) => Array<T | U>;
    interface LodashFill1x12<T, U> {
        (start: number): LodashFill1x13<T, U>;
        (start: lodash.__, end: number): LodashFill1x14<T, U>;
        (start: number, end: number): Array<T | U>;
    }
    type LodashFill1x13<T, U> = (end: number) => Array<T | U>;
    type LodashFill1x14<T, U> = (start: number) => Array<T | U>;
    interface LodashFill2x8<U> {
        (start: number): LodashFill2x9<U>;
        (start: lodash.__, end: number): LodashFill2x10<U>;
        (start: number, end: number): LodashFill2x11<U>;
        <T>(start: lodash.__, end: lodash.__, value: T): LodashFill2x12<T, U>;
        <T>(start: number, end: lodash.__, value: T): LodashFill2x13<T, U>;
        <T>(start: lodash.__, end: number, value: T): LodashFill2x14<T, U>;
        <T>(start: number, end: number, value: T): lodash.List<T | U>;
    }
    interface LodashFill2x9<U> {
        (end: number): LodashFill2x11<U>;
        <T>(end: lodash.__, value: T): LodashFill2x13<T, U>;
        <T>(end: number, value: T): lodash.List<T | U>;
    }
    interface LodashFill2x10<U> {
        (start: number): LodashFill2x11<U>;
        <T>(start: lodash.__, value: T): LodashFill2x14<T, U>;
        <T>(start: number, value: T): lodash.List<T | U>;
    }
    type LodashFill2x11<U> = <T>(value: T) => lodash.List<T | U>;
    interface LodashFill2x12<T, U> {
        (start: number): LodashFill2x13<T, U>;
        (start: lodash.__, end: number): LodashFill2x14<T, U>;
        (start: number, end: number): lodash.List<T | U>;
    }
    type LodashFill2x13<T, U> = (end: number) => lodash.List<T | U>;
    type LodashFill2x14<T, U> = (start: number) => lodash.List<T | U>;
    interface LodashFilter {
        <T, S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>): LodashFilter1x1<T, S>;
        <T>(predicate: lodash.__, collection: lodash.List<T> | null | undefined): LodashFilter1x2<T>;
        <T, S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>, collection: lodash.List<T> | null | undefined): S[];
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>): LodashFilter2x1<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, collection: lodash.List<T> | null | undefined): T[];
        <T extends object, S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>): LodashFilter3x1<T, S>;
        <T extends object>(predicate: lodash.__, collection: T | null | undefined): LodashFilter3x2<T>;
        <T extends object, S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>, collection: T | null | undefined): S[];
        <T extends object>(predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>, collection: T | null | undefined): Array<T[keyof T]>;
    }
    type LodashFilter1x1<T, S> = (collection: lodash.List<T> | null | undefined) => S[];
    interface LodashFilter1x2<T> {
        <S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>): S[];
        (predicate: lodash.ValueIterateeCustom<T, boolean>): T[];
    }
    type LodashFilter2x1<T> = (collection: lodash.List<T> | object | null | undefined) => T[];
    type LodashFilter3x1<T, S> = (collection: T | null | undefined) => S[];
    interface LodashFilter3x2<T> {
        <S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>): S[];
        (predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>): Array<T[keyof T]>;
    }
    interface LodashFind {
        <T, S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>): LodashFind1x1<T, S>;
        <T>(predicate: lodash.__, collection: lodash.List<T> | null | undefined): LodashFind1x2<T>;
        <T, S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>, collection: lodash.List<T> | null | undefined): S|undefined;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>): LodashFind2x1<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, collection: lodash.List<T> | null | undefined): T|undefined;
        <T extends object, S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>): LodashFind3x1<T, S>;
        <T extends object>(predicate: lodash.__, collection: T | null | undefined): LodashFind3x2<T>;
        <T extends object, S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>, collection: T | null | undefined): S|undefined;
        <T extends object>(predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>, collection: T | null | undefined): T[keyof T]|undefined;
    }
    type LodashFind1x1<T, S> = (collection: lodash.List<T> | null | undefined) => S|undefined;
    interface LodashFind1x2<T> {
        <S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>): S|undefined;
        (predicate: lodash.ValueIterateeCustom<T, boolean>): T|undefined;
    }
    type LodashFind2x1<T> = (collection: lodash.List<T> | object | null | undefined) => T|undefined;
    type LodashFind3x1<T, S> = (collection: T | null | undefined) => S|undefined;
    interface LodashFind3x2<T> {
        <S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>): S|undefined;
        (predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>): T[keyof T]|undefined;
    }
    interface LodashFindFrom {
        <T, S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>): LodashFindFrom1x1<T, S>;
        (predicate: lodash.__, fromIndex: number): LodashFindFrom1x2;
        <T, S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>, fromIndex: number): LodashFindFrom1x3<T, S>;
        <T>(predicate: lodash.__, fromIndex: lodash.__, collection: lodash.List<T> | null | undefined): LodashFindFrom1x4<T>;
        <T, S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>, fromIndex: lodash.__, collection: lodash.List<T> | null | undefined): LodashFindFrom1x5<S>;
        <T>(predicate: lodash.__, fromIndex: number, collection: lodash.List<T> | null | undefined): LodashFindFrom1x6<T>;
        <T, S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>, fromIndex: number, collection: lodash.List<T> | null | undefined): S|undefined;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>): LodashFindFrom2x1<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, fromIndex: number): LodashFindFrom2x3<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, fromIndex: lodash.__, collection: lodash.List<T> | null | undefined): LodashFindFrom2x5<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, fromIndex: number, collection: lodash.List<T> | null | undefined): T|undefined;
        <T extends object, S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>): LodashFindFrom3x1<T, S>;
        <T extends object, S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>, fromIndex: number): LodashFindFrom3x3<T, S>;
        <T extends object>(predicate: lodash.__, fromIndex: lodash.__, collection: T | null | undefined): LodashFindFrom3x4<T>;
        <T extends object, S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>, fromIndex: lodash.__, collection: T | null | undefined): LodashFindFrom3x5<S>;
        <T extends object>(predicate: lodash.__, fromIndex: number, collection: T | null | undefined): LodashFindFrom3x6<T>;
        <T extends object, S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>, fromIndex: number, collection: T | null | undefined): S|undefined;
        <T extends object>(predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>, fromIndex: lodash.__, collection: T | null | undefined): LodashFindFrom4x5<T>;
        <T extends object>(predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>, fromIndex: number, collection: T | null | undefined): T[keyof T]|undefined;
    }
    interface LodashFindFrom1x1<T, S> {
        (fromIndex: number): LodashFindFrom1x3<T, S>;
        (fromIndex: lodash.__, collection: lodash.List<T> | null | undefined): LodashFindFrom1x5<S>;
        (fromIndex: number, collection: lodash.List<T> | null | undefined): S|undefined;
    }
    interface LodashFindFrom1x2 {
        <T, S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>): LodashFindFrom1x3<T, S>;
        <T>(predicate: lodash.__, collection: lodash.List<T> | null | undefined): LodashFindFrom1x6<T>;
        <T, S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>, collection: lodash.List<T> | null | undefined): S|undefined;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>): LodashFindFrom2x3<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, collection: lodash.List<T> | null | undefined): T|undefined;
        <T extends object, S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>): LodashFindFrom3x3<T, S>;
        <T extends object>(predicate: lodash.__, collection: T | null | undefined): LodashFindFrom3x6<T>;
        <T extends object, S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>, collection: T | null | undefined): S|undefined;
        <T extends object>(predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>, collection: T | null | undefined): T[keyof T]|undefined;
    }
    type LodashFindFrom1x3<T, S> = (collection: lodash.List<T> | null | undefined) => S|undefined;
    interface LodashFindFrom1x4<T> {
        <S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>): LodashFindFrom1x5<S>;
        (predicate: lodash.__, fromIndex: number): LodashFindFrom1x6<T>;
        <S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>, fromIndex: number): S|undefined;
        (predicate: lodash.ValueIterateeCustom<T, boolean>): LodashFindFrom2x5<T>;
        (predicate: lodash.ValueIterateeCustom<T, boolean>, fromIndex: number): T|undefined;
    }
    type LodashFindFrom1x5<S> = (fromIndex: number) => S|undefined;
    interface LodashFindFrom1x6<T> {
        <S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>): S|undefined;
        (predicate: lodash.ValueIterateeCustom<T, boolean>): T|undefined;
    }
    interface LodashFindFrom2x1<T> {
        (fromIndex: number): LodashFindFrom2x3<T>;
        (fromIndex: lodash.__, collection: lodash.List<T> | null | undefined): LodashFindFrom2x5<T>;
        (fromIndex: number, collection: lodash.List<T> | object | null | undefined): T|undefined;
        <T1 extends object>(fromIndex: lodash.__, collection: T1 | null | undefined): LodashFindFrom4x5<T>;
    }
    interface LodashFindFrom2x3<T> {
        (collection: lodash.List<T> | null | undefined): T|undefined;
        (collection: object | null | undefined): object|undefined;
    }
    type LodashFindFrom2x5<T> = (fromIndex: number) => T|undefined;
    interface LodashFindFrom3x1<T, S> {
        (fromIndex: number): LodashFindFrom3x3<T, S>;
        (fromIndex: lodash.__, collection: T | null | undefined): LodashFindFrom3x5<S>;
        (fromIndex: number, collection: T | null | undefined): S|undefined;
    }
    type LodashFindFrom3x3<T, S> = (collection: T | null | undefined) => S|undefined;
    interface LodashFindFrom3x4<T> {
        <S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>): LodashFindFrom3x5<S>;
        (predicate: lodash.__, fromIndex: number): LodashFindFrom3x6<T>;
        <S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>, fromIndex: number): S|undefined;
        (predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>): LodashFindFrom4x5<T>;
        (predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>, fromIndex: number): T[keyof T]|undefined;
    }
    type LodashFindFrom3x5<S> = (fromIndex: number) => S|undefined;
    interface LodashFindFrom3x6<T> {
        <S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>): S|undefined;
        (predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>): T[keyof T]|undefined;
    }
    type LodashFindFrom4x5<T> = (fromIndex: number) => T[keyof T]|undefined;
    interface LodashFindIndex {
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>): LodashFindIndex1x1<T>;
        <T>(predicate: lodash.__, array: lodash.List<T> | null | undefined): LodashFindIndex1x2<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, array: lodash.List<T> | null | undefined): number;
    }
    type LodashFindIndex1x1<T> = (array: lodash.List<T> | null | undefined) => number;
    type LodashFindIndex1x2<T> = (predicate: lodash.ValueIterateeCustom<T, boolean>) => number;
    interface LodashFindIndexFrom {
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>): LodashFindIndexFrom1x1<T>;
        (predicate: lodash.__, fromIndex: number): LodashFindIndexFrom1x2;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, fromIndex: number): LodashFindIndexFrom1x3<T>;
        <T>(predicate: lodash.__, fromIndex: lodash.__, array: lodash.List<T> | null | undefined): LodashFindIndexFrom1x4<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, fromIndex: lodash.__, array: lodash.List<T> | null | undefined): LodashFindIndexFrom1x5;
        <T>(predicate: lodash.__, fromIndex: number, array: lodash.List<T> | null | undefined): LodashFindIndexFrom1x6<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, fromIndex: number, array: lodash.List<T> | null | undefined): number;
    }
    interface LodashFindIndexFrom1x1<T> {
        (fromIndex: number): LodashFindIndexFrom1x3<T>;
        (fromIndex: lodash.__, array: lodash.List<T> | null | undefined): LodashFindIndexFrom1x5;
        (fromIndex: number, array: lodash.List<T> | null | undefined): number;
    }
    interface LodashFindIndexFrom1x2 {
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>): LodashFindIndexFrom1x3<T>;
        <T>(predicate: lodash.__, array: lodash.List<T> | null | undefined): LodashFindIndexFrom1x6<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, array: lodash.List<T> | null | undefined): number;
    }
    type LodashFindIndexFrom1x3<T> = (array: lodash.List<T> | null | undefined) => number;
    interface LodashFindIndexFrom1x4<T> {
        (predicate: lodash.ValueIterateeCustom<T, boolean>): LodashFindIndexFrom1x5;
        (predicate: lodash.__, fromIndex: number): LodashFindIndexFrom1x6<T>;
        (predicate: lodash.ValueIterateeCustom<T, boolean>, fromIndex: number): number;
    }
    type LodashFindIndexFrom1x5 = (fromIndex: number) => number;
    type LodashFindIndexFrom1x6<T> = (predicate: lodash.ValueIterateeCustom<T, boolean>) => number;
    interface LodashFindKey {
        <T>(predicate: lodash.ValueIteratee<T>): LodashFindKey1x1<T>;
        <T>(predicate: lodash.__, object: T | null | undefined): LodashFindKey1x2<T>;
        <T>(predicate: lodash.ValueIteratee<T[keyof T]>, object: T | null | undefined): string | undefined;
    }
    type LodashFindKey1x1<T> = (object: object | null | undefined) => string | undefined;
    type LodashFindKey1x2<T> = (predicate: lodash.ValueIteratee<T[keyof T]>) => string | undefined;
    interface LodashFindLast {
        <T, S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>): LodashFindLast1x1<T, S>;
        <T>(predicate: lodash.__, collection: lodash.List<T> | null | undefined): LodashFindLast1x2<T>;
        <T, S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>, collection: lodash.List<T> | null | undefined): S|undefined;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>): LodashFindLast2x1<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, collection: lodash.List<T> | null | undefined): T|undefined;
        <T extends object, S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>): LodashFindLast3x1<T, S>;
        <T extends object>(predicate: lodash.__, collection: T | null | undefined): LodashFindLast3x2<T>;
        <T extends object, S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>, collection: T | null | undefined): S|undefined;
        <T extends object>(predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>, collection: T | null | undefined): T[keyof T]|undefined;
    }
    type LodashFindLast1x1<T, S> = (collection: lodash.List<T> | null | undefined) => S|undefined;
    interface LodashFindLast1x2<T> {
        <S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>): S|undefined;
        (predicate: lodash.ValueIterateeCustom<T, boolean>): T|undefined;
    }
    type LodashFindLast2x1<T> = (collection: lodash.List<T> | object | null | undefined) => T|undefined;
    type LodashFindLast3x1<T, S> = (collection: T | null | undefined) => S|undefined;
    interface LodashFindLast3x2<T> {
        <S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>): S|undefined;
        (predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>): T[keyof T]|undefined;
    }
    interface LodashFindLastFrom {
        <T, S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>): LodashFindLastFrom1x1<T, S>;
        (predicate: lodash.__, fromIndex: number): LodashFindLastFrom1x2;
        <T, S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>, fromIndex: number): LodashFindLastFrom1x3<T, S>;
        <T>(predicate: lodash.__, fromIndex: lodash.__, collection: lodash.List<T> | null | undefined): LodashFindLastFrom1x4<T>;
        <T, S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>, fromIndex: lodash.__, collection: lodash.List<T> | null | undefined): LodashFindLastFrom1x5<S>;
        <T>(predicate: lodash.__, fromIndex: number, collection: lodash.List<T> | null | undefined): LodashFindLastFrom1x6<T>;
        <T, S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>, fromIndex: number, collection: lodash.List<T> | null | undefined): S|undefined;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>): LodashFindLastFrom2x1<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, fromIndex: number): LodashFindLastFrom2x3<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, fromIndex: lodash.__, collection: lodash.List<T> | null | undefined): LodashFindLastFrom2x5<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, fromIndex: number, collection: lodash.List<T> | null | undefined): T|undefined;
        <T extends object, S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>): LodashFindLastFrom3x1<T, S>;
        <T extends object, S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>, fromIndex: number): LodashFindLastFrom3x3<T, S>;
        <T extends object>(predicate: lodash.__, fromIndex: lodash.__, collection: T | null | undefined): LodashFindLastFrom3x4<T>;
        <T extends object, S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>, fromIndex: lodash.__, collection: T | null | undefined): LodashFindLastFrom3x5<S>;
        <T extends object>(predicate: lodash.__, fromIndex: number, collection: T | null | undefined): LodashFindLastFrom3x6<T>;
        <T extends object, S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>, fromIndex: number, collection: T | null | undefined): S|undefined;
        <T extends object>(predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>, fromIndex: lodash.__, collection: T | null | undefined): LodashFindLastFrom4x5<T>;
        <T extends object>(predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>, fromIndex: number, collection: T | null | undefined): T[keyof T]|undefined;
    }
    interface LodashFindLastFrom1x1<T, S> {
        (fromIndex: number): LodashFindLastFrom1x3<T, S>;
        (fromIndex: lodash.__, collection: lodash.List<T> | null | undefined): LodashFindLastFrom1x5<S>;
        (fromIndex: number, collection: lodash.List<T> | null | undefined): S|undefined;
    }
    interface LodashFindLastFrom1x2 {
        <T, S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>): LodashFindLastFrom1x3<T, S>;
        <T>(predicate: lodash.__, collection: lodash.List<T> | null | undefined): LodashFindLastFrom1x6<T>;
        <T, S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>, collection: lodash.List<T> | null | undefined): S|undefined;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>): LodashFindLastFrom2x3<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, collection: lodash.List<T> | null | undefined): T|undefined;
        <T extends object, S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>): LodashFindLastFrom3x3<T, S>;
        <T extends object>(predicate: lodash.__, collection: T | null | undefined): LodashFindLastFrom3x6<T>;
        <T extends object, S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>, collection: T | null | undefined): S|undefined;
        <T extends object>(predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>, collection: T | null | undefined): T[keyof T]|undefined;
    }
    type LodashFindLastFrom1x3<T, S> = (collection: lodash.List<T> | null | undefined) => S|undefined;
    interface LodashFindLastFrom1x4<T> {
        <S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>): LodashFindLastFrom1x5<S>;
        (predicate: lodash.__, fromIndex: number): LodashFindLastFrom1x6<T>;
        <S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>, fromIndex: number): S|undefined;
        (predicate: lodash.ValueIterateeCustom<T, boolean>): LodashFindLastFrom2x5<T>;
        (predicate: lodash.ValueIterateeCustom<T, boolean>, fromIndex: number): T|undefined;
    }
    type LodashFindLastFrom1x5<S> = (fromIndex: number) => S|undefined;
    interface LodashFindLastFrom1x6<T> {
        <S extends T>(predicate: lodash.ValueIteratorTypeGuard<T, S>): S|undefined;
        (predicate: lodash.ValueIterateeCustom<T, boolean>): T|undefined;
    }
    interface LodashFindLastFrom2x1<T> {
        (fromIndex: number): LodashFindLastFrom2x3<T>;
        (fromIndex: lodash.__, collection: lodash.List<T> | null | undefined): LodashFindLastFrom2x5<T>;
        (fromIndex: number, collection: lodash.List<T> | object | null | undefined): T|undefined;
        <T1 extends object>(fromIndex: lodash.__, collection: T1 | null | undefined): LodashFindLastFrom4x5<T>;
    }
    interface LodashFindLastFrom2x3<T> {
        (collection: lodash.List<T> | null | undefined): T|undefined;
        (collection: object | null | undefined): object|undefined;
    }
    type LodashFindLastFrom2x5<T> = (fromIndex: number) => T|undefined;
    interface LodashFindLastFrom3x1<T, S> {
        (fromIndex: number): LodashFindLastFrom3x3<T, S>;
        (fromIndex: lodash.__, collection: T | null | undefined): LodashFindLastFrom3x5<S>;
        (fromIndex: number, collection: T | null | undefined): S|undefined;
    }
    type LodashFindLastFrom3x3<T, S> = (collection: T | null | undefined) => S|undefined;
    interface LodashFindLastFrom3x4<T> {
        <S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>): LodashFindLastFrom3x5<S>;
        (predicate: lodash.__, fromIndex: number): LodashFindLastFrom3x6<T>;
        <S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>, fromIndex: number): S|undefined;
        (predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>): LodashFindLastFrom4x5<T>;
        (predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>, fromIndex: number): T[keyof T]|undefined;
    }
    type LodashFindLastFrom3x5<S> = (fromIndex: number) => S|undefined;
    interface LodashFindLastFrom3x6<T> {
        <S extends T[keyof T]>(predicate: lodash.ValueIteratorTypeGuard<T[keyof T], S>): S|undefined;
        (predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>): T[keyof T]|undefined;
    }
    type LodashFindLastFrom4x5<T> = (fromIndex: number) => T[keyof T]|undefined;
    interface LodashFindLastIndex {
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>): LodashFindLastIndex1x1<T>;
        <T>(predicate: lodash.__, array: lodash.List<T> | null | undefined): LodashFindLastIndex1x2<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, array: lodash.List<T> | null | undefined): number;
    }
    type LodashFindLastIndex1x1<T> = (array: lodash.List<T> | null | undefined) => number;
    type LodashFindLastIndex1x2<T> = (predicate: lodash.ValueIterateeCustom<T, boolean>) => number;
    interface LodashFindLastIndexFrom {
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>): LodashFindLastIndexFrom1x1<T>;
        (predicate: lodash.__, fromIndex: number): LodashFindLastIndexFrom1x2;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, fromIndex: number): LodashFindLastIndexFrom1x3<T>;
        <T>(predicate: lodash.__, fromIndex: lodash.__, array: lodash.List<T> | null | undefined): LodashFindLastIndexFrom1x4<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, fromIndex: lodash.__, array: lodash.List<T> | null | undefined): LodashFindLastIndexFrom1x5;
        <T>(predicate: lodash.__, fromIndex: number, array: lodash.List<T> | null | undefined): LodashFindLastIndexFrom1x6<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, fromIndex: number, array: lodash.List<T> | null | undefined): number;
    }
    interface LodashFindLastIndexFrom1x1<T> {
        (fromIndex: number): LodashFindLastIndexFrom1x3<T>;
        (fromIndex: lodash.__, array: lodash.List<T> | null | undefined): LodashFindLastIndexFrom1x5;
        (fromIndex: number, array: lodash.List<T> | null | undefined): number;
    }
    interface LodashFindLastIndexFrom1x2 {
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>): LodashFindLastIndexFrom1x3<T>;
        <T>(predicate: lodash.__, array: lodash.List<T> | null | undefined): LodashFindLastIndexFrom1x6<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, array: lodash.List<T> | null | undefined): number;
    }
    type LodashFindLastIndexFrom1x3<T> = (array: lodash.List<T> | null | undefined) => number;
    interface LodashFindLastIndexFrom1x4<T> {
        (predicate: lodash.ValueIterateeCustom<T, boolean>): LodashFindLastIndexFrom1x5;
        (predicate: lodash.__, fromIndex: number): LodashFindLastIndexFrom1x6<T>;
        (predicate: lodash.ValueIterateeCustom<T, boolean>, fromIndex: number): number;
    }
    type LodashFindLastIndexFrom1x5 = (fromIndex: number) => number;
    type LodashFindLastIndexFrom1x6<T> = (predicate: lodash.ValueIterateeCustom<T, boolean>) => number;
    interface LodashFindLastKey {
        <T>(predicate: lodash.ValueIteratee<T>): LodashFindLastKey1x1<T>;
        <T>(predicate: lodash.__, object: T | null | undefined): LodashFindLastKey1x2<T>;
        <T>(predicate: lodash.ValueIteratee<T[keyof T]>, object: T | null | undefined): string | undefined;
    }
    type LodashFindLastKey1x1<T> = (object: object | null | undefined) => string | undefined;
    type LodashFindLastKey1x2<T> = (predicate: lodash.ValueIteratee<T[keyof T]>) => string | undefined;
    type LodashHead = <T>(array: lodash.List<T> | null | undefined) => T | undefined;
    interface LodashFlatMap {
        <T, TResult>(iteratee: (value: T) => lodash.Many<TResult>): LodashFlatMap1x1<T, TResult>;
        <T>(iteratee: lodash.__, collection: lodash.List<T> | null | undefined): LodashFlatMap1x2<T>;
        <T, TResult>(iteratee: (value: T) => lodash.Many<TResult>, collection: lodash.List<T> | null | undefined): TResult[];
        <T extends object, TResult>(iteratee: (value: T[keyof T]) => lodash.Many<TResult>): LodashFlatMap2x1<T, TResult>;
        <T extends object>(iteratee: lodash.__, collection: T | null | undefined): LodashFlatMap2x2<T>;
        <T extends object, TResult>(iteratee: (value: T[keyof T]) => lodash.Many<TResult>, collection: T | null | undefined): TResult[];
        (iteratee: string): LodashFlatMap3x1;
        (iteratee: lodash.__, collection: object | null | undefined): LodashFlatMap3x2;
        (iteratee: string, collection: object | null | undefined): any[];
        (iteratee: object): LodashFlatMap4x1;
        (iteratee: object, collection: object | null | undefined): boolean[];
    }
    type LodashFlatMap1x1<T, TResult> = (collection: lodash.List<T> | null | undefined) => TResult[];
    type LodashFlatMap1x2<T> = <TResult>(iteratee: (value: T) => lodash.Many<TResult>) => TResult[];
    type LodashFlatMap2x1<T, TResult> = (collection: T | null | undefined) => TResult[];
    type LodashFlatMap2x2<T> = <TResult>(iteratee: (value: T[keyof T]) => lodash.Many<TResult>) => TResult[];
    type LodashFlatMap3x1 = (collection: object | null | undefined) => any[];
    interface LodashFlatMap3x2 {
        (iteratee: string): any[];
        (iteratee: object): boolean[];
    }
    type LodashFlatMap4x1 = (collection: object | null | undefined) => boolean[];
    interface LodashFlatMapDeep {
        <T, TResult>(iteratee: (value: T) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult): LodashFlatMapDeep1x1<T, TResult>;
        <T>(iteratee: lodash.__, collection: lodash.List<T> | null | undefined): LodashFlatMapDeep1x2<T>;
        <T, TResult>(iteratee: (value: T) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult, collection: lodash.List<T> | null | undefined): TResult[];
        <T extends object, TResult>(iteratee: (value: T[keyof T]) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult): LodashFlatMapDeep2x1<T, TResult>;
        <T extends object>(iteratee: lodash.__, collection: T | null | undefined): LodashFlatMapDeep2x2<T>;
        <T extends object, TResult>(iteratee: (value: T[keyof T]) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult, collection: T | null | undefined): TResult[];
        (iteratee: string): LodashFlatMapDeep3x1;
        (iteratee: lodash.__, collection: object | null | undefined): LodashFlatMapDeep3x2;
        (iteratee: string, collection: object | null | undefined): any[];
        (iteratee: object): LodashFlatMapDeep4x1;
        (iteratee: object, collection: object | null | undefined): boolean[];
    }
    type LodashFlatMapDeep1x1<T, TResult> = (collection: lodash.List<T> | null | undefined) => TResult[];
    type LodashFlatMapDeep1x2<T> = <TResult>(iteratee: (value: T) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult) => TResult[];
    type LodashFlatMapDeep2x1<T, TResult> = (collection: T | null | undefined) => TResult[];
    type LodashFlatMapDeep2x2<T> = <TResult>(iteratee: (value: T[keyof T]) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult) => TResult[];
    type LodashFlatMapDeep3x1 = (collection: object | null | undefined) => any[];
    interface LodashFlatMapDeep3x2 {
        (iteratee: string): any[];
        (iteratee: object): boolean[];
    }
    type LodashFlatMapDeep4x1 = (collection: object | null | undefined) => boolean[];
    interface LodashFlatMapDepth {
        <T, TResult>(iteratee: (value: T) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult): LodashFlatMapDepth1x1<T, TResult>;
        (iteratee: lodash.__, depth: number): LodashFlatMapDepth1x2;
        <T, TResult>(iteratee: (value: T) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult, depth: number): LodashFlatMapDepth1x3<T, TResult>;
        <T>(iteratee: lodash.__, depth: lodash.__, collection: lodash.List<T> | null | undefined): LodashFlatMapDepth1x4<T>;
        <T, TResult>(iteratee: (value: T) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult, depth: lodash.__, collection: lodash.List<T> | null | undefined): LodashFlatMapDepth1x5<TResult>;
        <T>(iteratee: lodash.__, depth: number, collection: lodash.List<T> | null | undefined): LodashFlatMapDepth1x6<T>;
        <T, TResult>(iteratee: (value: T) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult, depth: number, collection: lodash.List<T> | null | undefined): TResult[];
        <T extends object, TResult>(iteratee: (value: T[keyof T]) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult): LodashFlatMapDepth2x1<T, TResult>;
        <T extends object, TResult>(iteratee: (value: T[keyof T]) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult, depth: number): LodashFlatMapDepth2x3<T, TResult>;
        <T extends object>(iteratee: lodash.__, depth: lodash.__, collection: T | null | undefined): LodashFlatMapDepth2x4<T>;
        <T extends object, TResult>(iteratee: (value: T[keyof T]) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult, depth: lodash.__, collection: T | null | undefined): LodashFlatMapDepth2x5<TResult>;
        <T extends object>(iteratee: lodash.__, depth: number, collection: T | null | undefined): LodashFlatMapDepth2x6<T>;
        <T extends object, TResult>(iteratee: (value: T[keyof T]) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult, depth: number, collection: T | null | undefined): TResult[];
        (iteratee: string): LodashFlatMapDepth3x1;
        (iteratee: string, depth: number): LodashFlatMapDepth3x3;
        (iteratee: lodash.__, depth: lodash.__, collection: object | null | undefined): LodashFlatMapDepth3x4;
        (iteratee: string, depth: lodash.__, collection: object | null | undefined): LodashFlatMapDepth3x5;
        (iteratee: lodash.__, depth: number, collection: object | null | undefined): LodashFlatMapDepth3x6;
        (iteratee: string, depth: number, collection: object | null | undefined): any[];
        (iteratee: object): LodashFlatMapDepth4x1;
        (iteratee: object, depth: number): LodashFlatMapDepth4x3;
        (iteratee: object, depth: lodash.__, collection: object | null | undefined): LodashFlatMapDepth4x5;
        (iteratee: object, depth: number, collection: object | null | undefined): boolean[];
    }
    interface LodashFlatMapDepth1x1<T, TResult> {
        (depth: number): LodashFlatMapDepth1x3<T, TResult>;
        (depth: lodash.__, collection: lodash.List<T> | null | undefined): LodashFlatMapDepth1x5<TResult>;
        (depth: number, collection: lodash.List<T> | null | undefined): TResult[];
    }
    interface LodashFlatMapDepth1x2 {
        <T, TResult>(iteratee: (value: T) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult): LodashFlatMapDepth1x3<T, TResult>;
        <T>(iteratee: lodash.__, collection: lodash.List<T> | null | undefined): LodashFlatMapDepth1x6<T>;
        <T, TResult>(iteratee: (value: T) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult, collection: lodash.List<T> | null | undefined): TResult[];
        <T extends object, TResult>(iteratee: (value: T[keyof T]) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult): LodashFlatMapDepth2x3<T, TResult>;
        <T extends object>(iteratee: lodash.__, collection: T | null | undefined): LodashFlatMapDepth2x6<T>;
        <T extends object, TResult>(iteratee: (value: T[keyof T]) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult, collection: T | null | undefined): TResult[];
        (iteratee: string): LodashFlatMapDepth3x3;
        (iteratee: lodash.__, collection: object | null | undefined): LodashFlatMapDepth3x6;
        (iteratee: string, collection: object | null | undefined): any[];
        (iteratee: object): LodashFlatMapDepth4x3;
        (iteratee: object, collection: object | null | undefined): boolean[];
    }
    type LodashFlatMapDepth1x3<T, TResult> = (collection: lodash.List<T> | null | undefined) => TResult[];
    interface LodashFlatMapDepth1x4<T> {
        <TResult>(iteratee: (value: T) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult): LodashFlatMapDepth1x5<TResult>;
        (iteratee: lodash.__, depth: number): LodashFlatMapDepth1x6<T>;
        <TResult>(iteratee: (value: T) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult, depth: number): TResult[];
    }
    type LodashFlatMapDepth1x5<TResult> = (depth: number) => TResult[];
    type LodashFlatMapDepth1x6<T> = <TResult>(iteratee: (value: T) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult) => TResult[];
    interface LodashFlatMapDepth2x1<T, TResult> {
        (depth: number): LodashFlatMapDepth2x3<T, TResult>;
        (depth: lodash.__, collection: T | null | undefined): LodashFlatMapDepth2x5<TResult>;
        (depth: number, collection: T | null | undefined): TResult[];
    }
    type LodashFlatMapDepth2x3<T, TResult> = (collection: T | null | undefined) => TResult[];
    interface LodashFlatMapDepth2x4<T> {
        <TResult>(iteratee: (value: T[keyof T]) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult): LodashFlatMapDepth2x5<TResult>;
        (iteratee: lodash.__, depth: number): LodashFlatMapDepth2x6<T>;
        <TResult>(iteratee: (value: T[keyof T]) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult, depth: number): TResult[];
    }
    type LodashFlatMapDepth2x5<TResult> = (depth: number) => TResult[];
    type LodashFlatMapDepth2x6<T> = <TResult>(iteratee: (value: T[keyof T]) => lodash.ListOfRecursiveArraysOrValues<TResult> | TResult) => TResult[];
    interface LodashFlatMapDepth3x1 {
        (depth: number): LodashFlatMapDepth3x3;
        (depth: lodash.__, collection: object | null | undefined): LodashFlatMapDepth3x5;
        (depth: number, collection: object | null | undefined): any[];
    }
    type LodashFlatMapDepth3x3 = (collection: object | null | undefined) => any[];
    interface LodashFlatMapDepth3x4 {
        (iteratee: string): LodashFlatMapDepth3x5;
        (iteratee: lodash.__, depth: number): LodashFlatMapDepth3x6;
        (iteratee: string, depth: number): any[];
        (iteratee: object): LodashFlatMapDepth4x5;
        (iteratee: object, depth: number): boolean[];
    }
    type LodashFlatMapDepth3x5 = (depth: number) => any[];
    interface LodashFlatMapDepth3x6 {
        (iteratee: string): any[];
        (iteratee: object): boolean[];
    }
    interface LodashFlatMapDepth4x1 {
        (depth: number): LodashFlatMapDepth4x3;
        (depth: lodash.__, collection: object | null | undefined): LodashFlatMapDepth4x5;
        (depth: number, collection: object | null | undefined): boolean[];
    }
    type LodashFlatMapDepth4x3 = (collection: object | null | undefined) => boolean[];
    type LodashFlatMapDepth4x5 = (depth: number) => boolean[];
    type LodashFlatten = <T>(array: lodash.List<lodash.Many<T>> | null | undefined) => T[];
    type LodashFlattenDeep = <T>(array: lodash.ListOfRecursiveArraysOrValues<T> | null | undefined) => T[];
    interface LodashFlattenDepth {
        (depth: number): LodashFlattenDepth1x1;
        <T>(depth: lodash.__, array: lodash.ListOfRecursiveArraysOrValues<T> | null | undefined): LodashFlattenDepth1x2<T>;
        <T>(depth: number, array: lodash.ListOfRecursiveArraysOrValues<T> | null | undefined): T[];
    }
    type LodashFlattenDepth1x1 = <T>(array: lodash.ListOfRecursiveArraysOrValues<T> | null | undefined) => T[];
    type LodashFlattenDepth1x2<T> = (depth: number) => T[];
    type LodashFlip = <T extends (...args: any) => any>(func: T) => T;
    type LodashFloor = (n: number) => number;
    interface LodashFlow {
        <A extends any[], R1, R2, R3, R4, R5, R6, R7>(f1: (...args: A) => R1, f2: (a: R1) => R2, f3: (a: R2) => R3, f4: (a: R3) => R4, f5: (a: R4) => R5, f6: (a: R5) => R6, f7: (a: R6) => R7): (...args: A) => R7;
        <A extends any[], R1, R2, R3, R4, R5, R6, R7>(f1: (...args: A) => R1, f2: (a: R1) => R2, f3: (a: R2) => R3, f4: (a: R3) => R4, f5: (a: R4) => R5, f6: (a: R5) => R6, f7: (a: R6) => R7, ...func: Array<lodash.Many<(a: any) => any>>): (...args: A) => any;
        <A extends any[], R1, R2, R3, R4, R5, R6>(f1: (...args: A) => R1, f2: (a: R1) => R2, f3: (a: R2) => R3, f4: (a: R3) => R4, f5: (a: R4) => R5, f6: (a: R5) => R6): (...args: A) => R6;
        <A extends any[], R1, R2, R3, R4, R5>(f1: (...args: A) => R1, f2: (a: R1) => R2, f3: (a: R2) => R3, f4: (a: R3) => R4, f5: (a: R4) => R5): (...args: A) => R5;
        <A extends any[], R1, R2, R3, R4>(f1: (...args: A) => R1, f2: (a: R1) => R2, f3: (a: R2) => R3, f4: (a: R3) => R4): (...args: A) => R4;
        <A extends any[], R1, R2, R3>(f1: (...args: A) => R1, f2: (a: R1) => R2, f3: (a: R2) => R3): (...args: A) => R3;
        <A extends any[], R1, R2>(f1: (...args: A) => R1, f2: (a: R1) => R2): (...args: A) => R2;
        (...func: Array<lodash.Many<(...args: any[]) => any>>): (...args: any[]) => any;
    }
    interface LodashForIn {
        <T>(iteratee: (value: T) => any): LodashForIn1x1<T>;
        <T>(iteratee: lodash.__, object: T): LodashForIn1x2<T>;
        <T>(iteratee: (value: T[keyof T]) => any, object: T): T;
        <T>(iteratee: lodash.__, object: T | null | undefined): LodashForIn2x2<T>;
        <T>(iteratee: (value: T[keyof T]) => any, object: T | null | undefined): T | null | undefined;
    }
    interface LodashForIn1x1<T> {
        <T1 extends object>(object: T1): T1;
        <T1 extends object>(object: T1 | null | undefined): T1 | null | undefined;
    }
    type LodashForIn1x2<T> = (iteratee: (value: T[keyof T]) => any) => T;
    type LodashForIn2x2<T> = (iteratee: (value: T[keyof T]) => any) => T | null | undefined;
    interface LodashForInRight {
        <T>(iteratee: (value: T) => any): LodashForInRight1x1<T>;
        <T>(iteratee: lodash.__, object: T): LodashForInRight1x2<T>;
        <T>(iteratee: (value: T[keyof T]) => any, object: T): T;
        <T>(iteratee: lodash.__, object: T | null | undefined): LodashForInRight2x2<T>;
        <T>(iteratee: (value: T[keyof T]) => any, object: T | null | undefined): T | null | undefined;
    }
    interface LodashForInRight1x1<T> {
        <T1 extends object>(object: T1): T1;
        <T1 extends object>(object: T1 | null | undefined): T1 | null | undefined;
    }
    type LodashForInRight1x2<T> = (iteratee: (value: T[keyof T]) => any) => T;
    type LodashForInRight2x2<T> = (iteratee: (value: T[keyof T]) => any) => T | null | undefined;
    interface LodashForOwn {
        <T>(iteratee: (value: T) => any): LodashForOwn1x1<T>;
        <T>(iteratee: lodash.__, object: T): LodashForOwn1x2<T>;
        <T>(iteratee: (value: T[keyof T]) => any, object: T): T;
        <T>(iteratee: lodash.__, object: T | null | undefined): LodashForOwn2x2<T>;
        <T>(iteratee: (value: T[keyof T]) => any, object: T | null | undefined): T | null | undefined;
    }
    interface LodashForOwn1x1<T> {
        <T1 extends object>(object: T1): T1;
        <T1 extends object>(object: T1 | null | undefined): T1 | null | undefined;
    }
    type LodashForOwn1x2<T> = (iteratee: (value: T[keyof T]) => any) => T;
    type LodashForOwn2x2<T> = (iteratee: (value: T[keyof T]) => any) => T | null | undefined;
    interface LodashForOwnRight {
        <T>(iteratee: (value: T) => any): LodashForOwnRight1x1<T>;
        <T>(iteratee: lodash.__, object: T): LodashForOwnRight1x2<T>;
        <T>(iteratee: (value: T[keyof T]) => any, object: T): T;
        <T>(iteratee: lodash.__, object: T | null | undefined): LodashForOwnRight2x2<T>;
        <T>(iteratee: (value: T[keyof T]) => any, object: T | null | undefined): T | null | undefined;
    }
    interface LodashForOwnRight1x1<T> {
        <T1 extends object>(object: T1): T1;
        <T1 extends object>(object: T1 | null | undefined): T1 | null | undefined;
    }
    type LodashForOwnRight1x2<T> = (iteratee: (value: T[keyof T]) => any) => T;
    type LodashForOwnRight2x2<T> = (iteratee: (value: T[keyof T]) => any) => T | null | undefined;
    interface LodashFromPairs {
        <T>(pairs: lodash.List<[lodash.PropertyName, T]> | null | undefined): lodash.Dictionary<T>;
        (pairs: lodash.List<any[]> | null | undefined): lodash.Dictionary<any>;
    }
    type LodashFunctions = (object: any) => string[];
    type LodashFunctionsIn = (object: any) => string[];
    interface LodashGet {
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey]): LodashGet1x1<TObject, TKey>;
        <TObject extends object>(path: lodash.__, object: TObject): LodashGet1x2<TObject>;
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey], object: TObject): TObject[TKey];
        <TObject extends object>(path: lodash.__, object: TObject | null | undefined): LodashGet2x2<TObject>;
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey], object: TObject | null | undefined): TObject[TKey] | undefined;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): LodashGet3x1<TObject, TKey1, TKey2>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2], object: TObject): TObject[TKey1][TKey2];
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2], object: TObject | null | undefined): TObject[TKey1][TKey2] | undefined;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): LodashGet5x1<TObject, TKey1, TKey2, TKey3>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3], object: TObject): TObject[TKey1][TKey2][TKey3];
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3], object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3] | undefined;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): LodashGet7x1<TObject, TKey1, TKey2, TKey3, TKey4>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4], object: TObject): TObject[TKey1][TKey2][TKey3][TKey4];
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4], object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3][TKey4] | undefined;
        (path: number): LodashGet9x1;
        <T>(path: lodash.__, object: lodash.NumericDictionary<T>): LodashGet9x2<T>;
        <T>(path: number, object: lodash.NumericDictionary<T>): T;
        <T>(path: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashGet10x2<T>;
        <T>(path: number, object: lodash.NumericDictionary<T> | null | undefined): T | undefined;
        (path: lodash.PropertyPath): LodashGet11x1;
        (path: lodash.__, object: null | undefined): LodashGet11x2;
        (path: lodash.PropertyPath, object: null | undefined): undefined;
        (path: lodash.__, object: any): LodashGet12x2;
        (path: lodash.PropertyPath, object: any): any;
    }
    interface LodashGet1x1<TObject, TKey extends keyof TObject> {
        (object: TObject): TObject[TKey];
        (object: TObject | null | undefined): TObject[TKey] | undefined;
    }
    interface LodashGet1x2<TObject> {
        <TKey extends keyof TObject>(path: TKey | [TKey]): TObject[TKey];
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): TObject[TKey1][TKey2];
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): TObject[TKey1][TKey2][TKey3];
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): TObject[TKey1][TKey2][TKey3][TKey4];
    }
    interface LodashGet2x2<TObject> {
        <TKey extends keyof TObject>(path: TKey | [TKey]): TObject[TKey] | undefined;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): TObject[TKey1][TKey2] | undefined;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): TObject[TKey1][TKey2][TKey3] | undefined;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): TObject[TKey1][TKey2][TKey3][TKey4] | undefined;
    }
    interface LodashGet3x1<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]> {
        (object: TObject): TObject[TKey1][TKey2];
        (object: TObject | null | undefined): TObject[TKey1][TKey2] | undefined;
    }
    interface LodashGet5x1<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]> {
        (object: TObject): TObject[TKey1][TKey2][TKey3];
        (object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3] | undefined;
    }
    interface LodashGet7x1<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]> {
        (object: TObject): TObject[TKey1][TKey2][TKey3][TKey4];
        (object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3][TKey4] | undefined;
    }
    interface LodashGet9x1 {
        <T>(object: lodash.NumericDictionary<T>): T;
        <T>(object: lodash.NumericDictionary<T> | null | undefined): T | undefined;
    }
    type LodashGet9x2<T> = (path: number) => T;
    type LodashGet10x2<T> = (path: number) => T | undefined;
    interface LodashGet11x1 {
        (object: null | undefined): undefined;
        (object: any): any;
    }
    type LodashGet11x2 = (path: lodash.PropertyPath) => undefined;
    type LodashGet12x2 = (path: lodash.PropertyPath) => any;
    interface LodashGetOr {
        <TDefault>(defaultValue: TDefault): LodashGetOr1x1<TDefault>;
        <TObject extends object, TKey extends keyof TObject>(defaultValue: lodash.__, path: TKey | [TKey]): LodashGetOr1x2<TObject, TKey>;
        <TObject extends object, TKey extends keyof TObject, TDefault>(defaultValue: TDefault, path: TKey | [TKey]): LodashGetOr1x3<TObject, TKey, TDefault>;
        <TObject extends object>(defaultValue: lodash.__, path: lodash.__, object: TObject | null | undefined): LodashGetOr1x4<TObject>;
        <TObject extends object, TDefault>(defaultValue: TDefault, path: lodash.__, object: TObject | null | undefined): LodashGetOr1x5<TObject, TDefault>;
        <TObject extends object, TKey extends keyof TObject>(defaultValue: lodash.__, path: TKey | [TKey], object: TObject | null | undefined): LodashGetOr1x6<TObject, TKey>;
        <TObject extends object, TKey extends keyof TObject, TDefault>(defaultValue: TDefault, path: TKey | [TKey], object: TObject | null | undefined): Exclude<TObject[TKey], undefined> | TDefault;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(defaultValue: lodash.__, path: [TKey1, TKey2]): LodashGetOr2x2<TObject, TKey1, TKey2>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2]): LodashGetOr2x3<TObject, TKey1, TKey2, TDefault>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(defaultValue: lodash.__, path: [TKey1, TKey2], object: TObject | null | undefined): LodashGetOr2x6<TObject, TKey1, TKey2>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2], object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(defaultValue: lodash.__, path: [TKey1, TKey2, TKey3]): LodashGetOr3x2<TObject, TKey1, TKey2, TKey3>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2, TKey3]): LodashGetOr3x3<TObject, TKey1, TKey2, TKey3, TDefault>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(defaultValue: lodash.__, path: [TKey1, TKey2, TKey3], object: TObject | null | undefined): LodashGetOr3x6<TObject, TKey1, TKey2, TKey3>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2, TKey3], object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(defaultValue: lodash.__, path: [TKey1, TKey2, TKey3, TKey4]): LodashGetOr4x2<TObject, TKey1, TKey2, TKey3, TKey4>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2, TKey3, TKey4]): LodashGetOr4x3<TObject, TKey1, TKey2, TKey3, TKey4, TDefault>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(defaultValue: lodash.__, path: [TKey1, TKey2, TKey3, TKey4], object: TObject | null | undefined): LodashGetOr4x6<TObject, TKey1, TKey2, TKey3, TKey4>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2, TKey3, TKey4], object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
        (defaultValue: lodash.__, path: number): LodashGetOr5x2;
        <TDefault>(defaultValue: TDefault, path: number): LodashGetOr5x3<TDefault>;
        <T>(defaultValue: lodash.__, path: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashGetOr5x4<T>;
        <T, TDefault>(defaultValue: TDefault, path: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashGetOr5x5<T, TDefault>;
        <T>(defaultValue: lodash.__, path: number, object: lodash.NumericDictionary<T> | null | undefined): LodashGetOr5x6<T>;
        <T, TDefault>(defaultValue: TDefault, path: number, object: lodash.NumericDictionary<T> | null | undefined): T | TDefault;
        (defaultValue: lodash.__, path: lodash.PropertyPath): LodashGetOr6x2;
        <TDefault>(defaultValue: TDefault, path: lodash.PropertyPath): LodashGetOr6x3<TDefault>;
        (defaultValue: lodash.__, path: lodash.__, object: null | undefined): LodashGetOr6x4;
        <TDefault>(defaultValue: TDefault, path: lodash.__, object: null | undefined): LodashGetOr6x5<TDefault>;
        (defaultValue: lodash.__, path: lodash.PropertyPath, object: null | undefined): LodashGetOr6x6;
        <TDefault>(defaultValue: TDefault, path: lodash.PropertyPath, object: null | undefined): TDefault;
        (defaultValue: any): LodashGetOr7x1;
        (defaultValue: any, path: lodash.PropertyPath): LodashGetOr7x3;
        (defaultValue: lodash.__, path: lodash.__, object: any): LodashGetOr7x4;
        (defaultValue: any, path: lodash.__, object: any): LodashGetOr7x5;
        (defaultValue: lodash.__, path: lodash.PropertyPath, object: any): LodashGetOr7x6;
        (defaultValue: any, path: lodash.PropertyPath, object: any): any;
    }
    interface LodashGetOr1x1<TDefault> {
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey]): LodashGetOr1x3<TObject, TKey, TDefault>;
        <TObject extends object>(path: lodash.__, object: TObject | null | undefined): LodashGetOr1x5<TObject, TDefault>;
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey], object: TObject | null | undefined): Exclude<TObject[TKey], undefined> | TDefault;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): LodashGetOr2x3<TObject, TKey1, TKey2, TDefault>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2], object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): LodashGetOr3x3<TObject, TKey1, TKey2, TKey3, TDefault>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3], object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): LodashGetOr4x3<TObject, TKey1, TKey2, TKey3, TKey4, TDefault>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4], object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
        (path: number): LodashGetOr5x3<TDefault>;
        <T>(path: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashGetOr5x5<T, TDefault>;
        <T>(path: number, object: lodash.NumericDictionary<T> | null | undefined): T | TDefault;
        (path: lodash.PropertyPath): LodashGetOr6x3<TDefault>;
        (path: lodash.__, object: null | undefined): LodashGetOr6x5<TDefault>;
        (path: lodash.PropertyPath, object: null | undefined): TDefault;
    }
    interface LodashGetOr1x2<TObject, TKey extends keyof TObject> {
        <TDefault>(defaultValue: TDefault): LodashGetOr1x3<TObject, TKey, TDefault>;
        (defaultValue: lodash.__, object: TObject | null | undefined): LodashGetOr1x6<TObject, TKey>;
        <TDefault>(defaultValue: TDefault, object: TObject | null | undefined): Exclude<TObject[TKey], undefined> | TDefault;
    }
    type LodashGetOr1x3<TObject, TKey extends keyof TObject, TDefault> = (object: TObject | null | undefined) => Exclude<TObject[TKey], undefined> | TDefault;
    interface LodashGetOr1x4<TObject> {
        <TDefault>(defaultValue: TDefault): LodashGetOr1x5<TObject, TDefault>;
        <TKey extends keyof TObject>(defaultValue: lodash.__, path: TKey | [TKey]): LodashGetOr1x6<TObject, TKey>;
        <TKey extends keyof TObject, TDefault>(defaultValue: TDefault, path: TKey | [TKey]): Exclude<TObject[TKey], undefined> | TDefault;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(defaultValue: lodash.__, path: [TKey1, TKey2]): LodashGetOr2x6<TObject, TKey1, TKey2>;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2]): Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(defaultValue: lodash.__, path: [TKey1, TKey2, TKey3]): LodashGetOr3x6<TObject, TKey1, TKey2, TKey3>;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2, TKey3]): Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(defaultValue: lodash.__, path: [TKey1, TKey2, TKey3, TKey4]): LodashGetOr4x6<TObject, TKey1, TKey2, TKey3, TKey4>;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2, TKey3, TKey4]): Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
    }
    interface LodashGetOr1x5<TObject, TDefault> {
        <TKey extends keyof TObject>(path: TKey | [TKey]): Exclude<TObject[TKey], undefined> | TDefault;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
    }
    type LodashGetOr1x6<TObject, TKey extends keyof TObject> = <TDefault>(defaultValue: TDefault) => Exclude<TObject[TKey], undefined> | TDefault;
    interface LodashGetOr2x2<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]> {
        <TDefault>(defaultValue: TDefault): LodashGetOr2x3<TObject, TKey1, TKey2, TDefault>;
        (defaultValue: lodash.__, object: TObject | null | undefined): LodashGetOr2x6<TObject, TKey1, TKey2>;
        <TDefault>(defaultValue: TDefault, object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
    }
    type LodashGetOr2x3<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TDefault> = (object: TObject | null | undefined) => Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
    type LodashGetOr2x6<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]> = <TDefault>(defaultValue: TDefault) => Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
    interface LodashGetOr3x2<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]> {
        <TDefault>(defaultValue: TDefault): LodashGetOr3x3<TObject, TKey1, TKey2, TKey3, TDefault>;
        (defaultValue: lodash.__, object: TObject | null | undefined): LodashGetOr3x6<TObject, TKey1, TKey2, TKey3>;
        <TDefault>(defaultValue: TDefault, object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
    }
    type LodashGetOr3x3<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TDefault> = (object: TObject | null | undefined) => Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
    type LodashGetOr3x6<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]> = <TDefault>(defaultValue: TDefault) => Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
    interface LodashGetOr4x2<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]> {
        <TDefault>(defaultValue: TDefault): LodashGetOr4x3<TObject, TKey1, TKey2, TKey3, TKey4, TDefault>;
        (defaultValue: lodash.__, object: TObject | null | undefined): LodashGetOr4x6<TObject, TKey1, TKey2, TKey3, TKey4>;
        <TDefault>(defaultValue: TDefault, object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
    }
    type LodashGetOr4x3<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3], TDefault> = (object: TObject | null | undefined) => Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
    type LodashGetOr4x6<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]> = <TDefault>(defaultValue: TDefault) => Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
    interface LodashGetOr5x2 {
        <TDefault>(defaultValue: TDefault): LodashGetOr5x3<TDefault>;
        <T>(defaultValue: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashGetOr5x6<T>;
        <T, TDefault>(defaultValue: TDefault, object: lodash.NumericDictionary<T> | null | undefined): T | TDefault;
    }
    type LodashGetOr5x3<TDefault> = <T>(object: lodash.NumericDictionary<T> | null | undefined) => T | TDefault;
    interface LodashGetOr5x4<T> {
        <TDefault>(defaultValue: TDefault): LodashGetOr5x5<T, TDefault>;
        (defaultValue: lodash.__, path: number): LodashGetOr5x6<T>;
        <TDefault>(defaultValue: TDefault, path: number): T | TDefault;
    }
    type LodashGetOr5x5<T, TDefault> = (path: number) => T | TDefault;
    type LodashGetOr5x6<T> = <TDefault>(defaultValue: TDefault) => T | TDefault;
    interface LodashGetOr6x2 {
        <TDefault>(defaultValue: TDefault): LodashGetOr6x3<TDefault>;
        (defaultValue: lodash.__, object: null | undefined): LodashGetOr6x6;
        <TDefault>(defaultValue: TDefault, object: null | undefined): TDefault;
        (defaultValue: any): LodashGetOr7x3;
        (defaultValue: lodash.__, object: any): LodashGetOr7x6;
        (defaultValue: any, object: any): any;
    }
    type LodashGetOr6x3<TDefault> = (object: null | undefined) => TDefault;
    interface LodashGetOr6x4 {
        <TDefault>(defaultValue: TDefault): LodashGetOr6x5<TDefault>;
        (defaultValue: lodash.__, path: lodash.PropertyPath): LodashGetOr6x6;
        <TDefault>(defaultValue: TDefault, path: lodash.PropertyPath): TDefault;
    }
    type LodashGetOr6x5<TDefault> = (path: lodash.PropertyPath) => TDefault;
    type LodashGetOr6x6 = <TDefault>(defaultValue: TDefault) => TDefault;
    interface LodashGetOr7x1 {
        (path: lodash.PropertyPath): LodashGetOr7x3;
        (path: lodash.__, object: any): LodashGetOr7x5;
        (path: lodash.PropertyPath, object: any): any;
    }
    type LodashGetOr7x3 = (object: any) => any;
    interface LodashGetOr7x4 {
        (defaultValue: any): LodashGetOr7x5;
        (defaultValue: lodash.__, path: lodash.PropertyPath): LodashGetOr7x6;
        (defaultValue: any, path: lodash.PropertyPath): any;
    }
    type LodashGetOr7x5 = (path: lodash.PropertyPath) => any;
    type LodashGetOr7x6 = (defaultValue: any) => any;
    interface LodashGroupBy {
        <T>(iteratee: lodash.ValueIteratee<T>): LodashGroupBy1x1<T>;
        <T>(iteratee: lodash.__, collection: lodash.List<T> | null | undefined): LodashGroupBy1x2<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, collection: lodash.List<T> | null | undefined): lodash.Dictionary<T[]>;
        <T extends object>(iteratee: lodash.__, collection: T | null | undefined): LodashGroupBy2x2<T>;
        <T extends object>(iteratee: lodash.ValueIteratee<T[keyof T]>, collection: T | null | undefined): lodash.Dictionary<Array<T[keyof T]>>;
    }
    type LodashGroupBy1x1<T> = (collection: lodash.List<T> | object | null | undefined) => lodash.Dictionary<T[]>;
    type LodashGroupBy1x2<T> = (iteratee: lodash.ValueIteratee<T>) => lodash.Dictionary<T[]>;
    type LodashGroupBy2x2<T> = (iteratee: lodash.ValueIteratee<T[keyof T]>) => lodash.Dictionary<Array<T[keyof T]>>;
    interface LodashGt {
        (value: any): LodashGt1x1;
        (value: lodash.__, other: any): LodashGt1x2;
        (value: any, other: any): boolean;
    }
    type LodashGt1x1 = (other: any) => boolean;
    type LodashGt1x2 = (value: any) => boolean;
    interface LodashGte {
        (value: any): LodashGte1x1;
        (value: lodash.__, other: any): LodashGte1x2;
        (value: any, other: any): boolean;
    }
    type LodashGte1x1 = (other: any) => boolean;
    type LodashGte1x2 = (value: any) => boolean;
    interface LodashHas {
        (path: lodash.PropertyPath): LodashHas1x1;
        <T>(path: lodash.__, object: T): LodashHas1x2;
        <T>(path: lodash.PropertyPath, object: T): boolean;
    }
    type LodashHas1x1 = <T>(object: T) => boolean;
    type LodashHas1x2 = (path: lodash.PropertyPath) => boolean;
    interface LodashHasIn {
        (path: lodash.PropertyPath): LodashHasIn1x1;
        <T>(path: lodash.__, object: T): LodashHasIn1x2;
        <T>(path: lodash.PropertyPath, object: T): boolean;
    }
    type LodashHasIn1x1 = <T>(object: T) => boolean;
    type LodashHasIn1x2 = (path: lodash.PropertyPath) => boolean;
    interface LodashIdentity {
        <T>(value: T): T;
        (): undefined;
    }
    interface LodashIncludes {
        <T>(target: T): LodashIncludes1x1<T>;
        <T>(target: lodash.__, collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): LodashIncludes1x2<T>;
        <T>(target: T, collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): boolean;
    }
    type LodashIncludes1x1<T> = (collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined) => boolean;
    type LodashIncludes1x2<T> = (target: T) => boolean;
    interface LodashIncludesFrom {
        <T>(target: T): LodashIncludesFrom1x1<T>;
        (target: lodash.__, fromIndex: number): LodashIncludesFrom1x2;
        <T>(target: T, fromIndex: number): LodashIncludesFrom1x3<T>;
        <T>(target: lodash.__, fromIndex: lodash.__, collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): LodashIncludesFrom1x4<T>;
        <T>(target: T, fromIndex: lodash.__, collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): LodashIncludesFrom1x5;
        <T>(target: lodash.__, fromIndex: number, collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): LodashIncludesFrom1x6<T>;
        <T>(target: T, fromIndex: number, collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): boolean;
    }
    interface LodashIncludesFrom1x1<T> {
        (fromIndex: number): LodashIncludesFrom1x3<T>;
        (fromIndex: lodash.__, collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): LodashIncludesFrom1x5;
        (fromIndex: number, collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): boolean;
    }
    interface LodashIncludesFrom1x2 {
        <T>(target: T): LodashIncludesFrom1x3<T>;
        <T>(target: lodash.__, collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): LodashIncludesFrom1x6<T>;
        <T>(target: T, collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): boolean;
    }
    type LodashIncludesFrom1x3<T> = (collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined) => boolean;
    interface LodashIncludesFrom1x4<T> {
        (target: T): LodashIncludesFrom1x5;
        (target: lodash.__, fromIndex: number): LodashIncludesFrom1x6<T>;
        (target: T, fromIndex: number): boolean;
    }
    type LodashIncludesFrom1x5 = (fromIndex: number) => boolean;
    type LodashIncludesFrom1x6<T> = (target: T) => boolean;
    interface LodashKeyBy {
        <T>(iteratee: lodash.ValueIterateeCustom<T, lodash.PropertyName>): LodashKeyBy1x1<T>;
        <T>(iteratee: lodash.__, collection: lodash.List<T> | null | undefined): LodashKeyBy1x2<T>;
        <T>(iteratee: lodash.ValueIterateeCustom<T, lodash.PropertyName>, collection: lodash.List<T> | null | undefined): lodash.Dictionary<T>;
        <T extends object>(iteratee: lodash.__, collection: T | null | undefined): LodashKeyBy2x2<T>;
        <T extends object>(iteratee: lodash.ValueIterateeCustom<T[keyof T], lodash.PropertyName>, collection: T | null | undefined): lodash.Dictionary<T[keyof T]>;
    }
    type LodashKeyBy1x1<T> = (collection: lodash.List<T> | object | null | undefined) => lodash.Dictionary<T>;
    type LodashKeyBy1x2<T> = (iteratee: lodash.ValueIterateeCustom<T, lodash.PropertyName>) => lodash.Dictionary<T>;
    type LodashKeyBy2x2<T> = (iteratee: lodash.ValueIterateeCustom<T[keyof T], lodash.PropertyName>) => lodash.Dictionary<T[keyof T]>;
    interface LodashIndexOf {
        <T>(value: T): LodashIndexOf1x1<T>;
        <T>(value: lodash.__, array: lodash.List<T> | null | undefined): LodashIndexOf1x2<T>;
        <T>(value: T, array: lodash.List<T> | null | undefined): number;
    }
    type LodashIndexOf1x1<T> = (array: lodash.List<T> | null | undefined) => number;
    type LodashIndexOf1x2<T> = (value: T) => number;
    interface LodashIndexOfFrom {
        <T>(value: T): LodashIndexOfFrom1x1<T>;
        (value: lodash.__, fromIndex: number): LodashIndexOfFrom1x2;
        <T>(value: T, fromIndex: number): LodashIndexOfFrom1x3<T>;
        <T>(value: lodash.__, fromIndex: lodash.__, array: lodash.List<T> | null | undefined): LodashIndexOfFrom1x4<T>;
        <T>(value: T, fromIndex: lodash.__, array: lodash.List<T> | null | undefined): LodashIndexOfFrom1x5;
        <T>(value: lodash.__, fromIndex: number, array: lodash.List<T> | null | undefined): LodashIndexOfFrom1x6<T>;
        <T>(value: T, fromIndex: number, array: lodash.List<T> | null | undefined): number;
    }
    interface LodashIndexOfFrom1x1<T> {
        (fromIndex: number): LodashIndexOfFrom1x3<T>;
        (fromIndex: lodash.__, array: lodash.List<T> | null | undefined): LodashIndexOfFrom1x5;
        (fromIndex: number, array: lodash.List<T> | null | undefined): number;
    }
    interface LodashIndexOfFrom1x2 {
        <T>(value: T): LodashIndexOfFrom1x3<T>;
        <T>(value: lodash.__, array: lodash.List<T> | null | undefined): LodashIndexOfFrom1x6<T>;
        <T>(value: T, array: lodash.List<T> | null | undefined): number;
    }
    type LodashIndexOfFrom1x3<T> = (array: lodash.List<T> | null | undefined) => number;
    interface LodashIndexOfFrom1x4<T> {
        (value: T): LodashIndexOfFrom1x5;
        (value: lodash.__, fromIndex: number): LodashIndexOfFrom1x6<T>;
        (value: T, fromIndex: number): number;
    }
    type LodashIndexOfFrom1x5 = (fromIndex: number) => number;
    type LodashIndexOfFrom1x6<T> = (value: T) => number;
    type LodashInitial = <T>(array: lodash.List<T> | null | undefined) => T[];
    interface LodashInRange {
        (start: number): LodashInRange1x1;
        (start: lodash.__, end: number): LodashInRange1x2;
        (start: number, end: number): LodashInRange1x3;
        (start: lodash.__, end: lodash.__, n: number): LodashInRange1x4;
        (start: number, end: lodash.__, n: number): LodashInRange1x5;
        (start: lodash.__, end: number, n: number): LodashInRange1x6;
        (start: number, end: number, n: number): boolean;
    }
    interface LodashInRange1x1 {
        (end: number): LodashInRange1x3;
        (end: lodash.__, n: number): LodashInRange1x5;
        (end: number, n: number): boolean;
    }
    interface LodashInRange1x2 {
        (start: number): LodashInRange1x3;
        (start: lodash.__, n: number): LodashInRange1x6;
        (start: number, n: number): boolean;
    }
    type LodashInRange1x3 = (n: number) => boolean;
    interface LodashInRange1x4 {
        (start: number): LodashInRange1x5;
        (start: lodash.__, end: number): LodashInRange1x6;
        (start: number, end: number): boolean;
    }
    type LodashInRange1x5 = (end: number) => boolean;
    type LodashInRange1x6 = (start: number) => boolean;
    interface LodashIntersection {
        <T>(arrays2: lodash.List<T> | null | undefined): LodashIntersection1x1<T>;
        <T>(arrays2: lodash.__, arrays: lodash.List<T> | null | undefined): LodashIntersection1x2<T>;
        <T>(arrays2: lodash.List<T> | null | undefined, arrays: lodash.List<T> | null | undefined): T[];
    }
    type LodashIntersection1x1<T> = (arrays: lodash.List<T> | null | undefined) => T[];
    type LodashIntersection1x2<T> = (arrays2: lodash.List<T> | null | undefined) => T[];
    interface LodashIntersectionBy {
        <T1, T2>(iteratee: lodash.ValueIteratee<T1 | T2>): LodashIntersectionBy1x1<T1, T2>;
        <T1>(iteratee: lodash.__, array: lodash.List<T1> | null): LodashIntersectionBy1x2<T1>;
        <T1, T2>(iteratee: lodash.ValueIteratee<T1 | T2>, array: lodash.List<T1> | null): LodashIntersectionBy1x3<T1, T2>;
        <T2>(iteratee: lodash.__, array: lodash.__, values: lodash.List<T2>): LodashIntersectionBy1x4<T2>;
        <T1, T2>(iteratee: lodash.ValueIteratee<T1 | T2>, array: lodash.__, values: lodash.List<T2>): LodashIntersectionBy1x5<T1>;
        <T1, T2>(iteratee: lodash.__, array: lodash.List<T1> | null, values: lodash.List<T2>): LodashIntersectionBy1x6<T1, T2>;
        <T1, T2>(iteratee: lodash.ValueIteratee<T1 | T2>, array: lodash.List<T1> | null, values: lodash.List<T2>): T1[];
    }
    interface LodashIntersectionBy1x1<T1, T2> {
        (array: lodash.List<T1> | null): LodashIntersectionBy1x3<T1, T2>;
        (array: lodash.__, values: lodash.List<T2>): LodashIntersectionBy1x5<T1>;
        (array: lodash.List<T1> | null, values: lodash.List<T2>): T1[];
    }
    interface LodashIntersectionBy1x2<T1> {
        <T2>(iteratee: lodash.ValueIteratee<T1 | T2>): LodashIntersectionBy1x3<T1, T2>;
        <T2>(iteratee: lodash.__, values: lodash.List<T2>): LodashIntersectionBy1x6<T1, T2>;
        <T2>(iteratee: lodash.ValueIteratee<T1 | T2>, values: lodash.List<T2>): T1[];
    }
    type LodashIntersectionBy1x3<T1, T2> = (values: lodash.List<T2>) => T1[];
    interface LodashIntersectionBy1x4<T2> {
        <T1>(iteratee: lodash.ValueIteratee<T1 | T2>): LodashIntersectionBy1x5<T1>;
        <T1>(iteratee: lodash.__, array: lodash.List<T1> | null): LodashIntersectionBy1x6<T1, T2>;
        <T1>(iteratee: lodash.ValueIteratee<T1 | T2>, array: lodash.List<T1> | null): T1[];
    }
    type LodashIntersectionBy1x5<T1> = (array: lodash.List<T1> | null) => T1[];
    type LodashIntersectionBy1x6<T1, T2> = (iteratee: lodash.ValueIteratee<T1 | T2>) => T1[];
    interface LodashIntersectionWith {
        <T1, T2>(comparator: lodash.Comparator2<T1, T2>): LodashIntersectionWith1x1<T1, T2>;
        <T1>(comparator: lodash.__, array: lodash.List<T1> | null | undefined): LodashIntersectionWith1x2<T1>;
        <T1, T2>(comparator: lodash.Comparator2<T1, T2>, array: lodash.List<T1> | null | undefined): LodashIntersectionWith1x3<T1, T2>;
        <T2>(comparator: lodash.__, array: lodash.__, values: lodash.List<T2>): LodashIntersectionWith1x4<T2>;
        <T1, T2>(comparator: lodash.Comparator2<T1, T2>, array: lodash.__, values: lodash.List<T2>): LodashIntersectionWith1x5<T1>;
        <T1, T2>(comparator: lodash.__, array: lodash.List<T1> | null | undefined, values: lodash.List<T2>): LodashIntersectionWith1x6<T1, T2>;
        <T1, T2>(comparator: lodash.Comparator2<T1, T2>, array: lodash.List<T1> | null | undefined, values: lodash.List<T2>): T1[];
    }
    interface LodashIntersectionWith1x1<T1, T2> {
        (array: lodash.List<T1> | null | undefined): LodashIntersectionWith1x3<T1, T2>;
        (array: lodash.__, values: lodash.List<T2>): LodashIntersectionWith1x5<T1>;
        (array: lodash.List<T1> | null | undefined, values: lodash.List<T2>): T1[];
    }
    interface LodashIntersectionWith1x2<T1> {
        <T2>(comparator: lodash.Comparator2<T1, T2>): LodashIntersectionWith1x3<T1, T2>;
        <T2>(comparator: lodash.__, values: lodash.List<T2>): LodashIntersectionWith1x6<T1, T2>;
        <T2>(comparator: lodash.Comparator2<T1, T2>, values: lodash.List<T2>): T1[];
    }
    type LodashIntersectionWith1x3<T1, T2> = (values: lodash.List<T2>) => T1[];
    interface LodashIntersectionWith1x4<T2> {
        <T1>(comparator: lodash.Comparator2<T1, T2>): LodashIntersectionWith1x5<T1>;
        <T1>(comparator: lodash.__, array: lodash.List<T1> | null | undefined): LodashIntersectionWith1x6<T1, T2>;
        <T1>(comparator: lodash.Comparator2<T1, T2>, array: lodash.List<T1> | null | undefined): T1[];
    }
    type LodashIntersectionWith1x5<T1> = (array: lodash.List<T1> | null | undefined) => T1[];
    type LodashIntersectionWith1x6<T1, T2> = (comparator: lodash.Comparator2<T1, T2>) => T1[];
    type LodashInvert = (object: object) => lodash.Dictionary<string>;
    interface LodashInvertBy {
        <T>(interatee: lodash.ValueIteratee<T>): LodashInvertBy1x1<T>;
        <T>(interatee: lodash.__, object:  lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): LodashInvertBy1x2<T>;
        <T>(interatee: lodash.ValueIteratee<T>, object:  lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): lodash.Dictionary<string[]>;
        <T extends object>(interatee: lodash.__, object: T | null | undefined): LodashInvertBy2x2<T>;
        <T extends object>(interatee: lodash.ValueIteratee<T[keyof T]>, object: T | null | undefined): lodash.Dictionary<string[]>;
    }
    type LodashInvertBy1x1<T> = (object: lodash.Dictionary<T> | lodash.NumericDictionary<T> | object | null | undefined) => lodash.Dictionary<string[]>;
    type LodashInvertBy1x2<T> = (interatee: lodash.ValueIteratee<T>) => lodash.Dictionary<string[]>;
    type LodashInvertBy2x2<T> = (interatee: lodash.ValueIteratee<T[keyof T]>) => lodash.Dictionary<string[]>;
    interface LodashInvoke {
        (path: lodash.PropertyPath): LodashInvoke1x1;
        (path: lodash.__, object: any): LodashInvoke1x2;
        (path: lodash.PropertyPath, object: any): any;
    }
    type LodashInvoke1x1 = (object: any) => any;
    type LodashInvoke1x2 = (path: lodash.PropertyPath) => any;
    interface LodashInvokeArgs {
        (path: lodash.PropertyPath): LodashInvokeArgs1x1;
        (path: lodash.__, args: ReadonlyArray<any>): LodashInvokeArgs1x2;
        (path: lodash.PropertyPath, args: ReadonlyArray<any>): LodashInvokeArgs1x3;
        (path: lodash.__, args: lodash.__, object: any): LodashInvokeArgs1x4;
        (path: lodash.PropertyPath, args: lodash.__, object: any): LodashInvokeArgs1x5;
        (path: lodash.__, args: ReadonlyArray<any>, object: any): LodashInvokeArgs1x6;
        (path: lodash.PropertyPath, args: ReadonlyArray<any>, object: any): any;
    }
    interface LodashInvokeArgs1x1 {
        (args: ReadonlyArray<any>): LodashInvokeArgs1x3;
        (args: lodash.__, object: any): LodashInvokeArgs1x5;
        (args: ReadonlyArray<any>, object: any): any;
    }
    interface LodashInvokeArgs1x2 {
        (path: lodash.PropertyPath): LodashInvokeArgs1x3;
        (path: lodash.__, object: any): LodashInvokeArgs1x6;
        (path: lodash.PropertyPath, object: any): any;
    }
    type LodashInvokeArgs1x3 = (object: any) => any;
    interface LodashInvokeArgs1x4 {
        (path: lodash.PropertyPath): LodashInvokeArgs1x5;
        (path: lodash.__, args: ReadonlyArray<any>): LodashInvokeArgs1x6;
        (path: lodash.PropertyPath, args: ReadonlyArray<any>): any;
    }
    type LodashInvokeArgs1x5 = (args: ReadonlyArray<any>) => any;
    type LodashInvokeArgs1x6 = (path: lodash.PropertyPath) => any;
    interface LodashInvokeArgsMap {
        (methodName: string): LodashInvokeArgsMap1x1;
        (methodNameOrMethod: lodash.__, args: ReadonlyArray<any>): LodashInvokeArgsMap1x2;
        (methodName: string, args: ReadonlyArray<any>): LodashInvokeArgsMap1x3;
        (methodNameOrMethod: lodash.__, args: lodash.__, collection: object | null | undefined): LodashInvokeArgsMap1x4;
        (methodName: string, args: lodash.__, collection: object | null | undefined): LodashInvokeArgsMap1x5;
        (methodNameOrMethod: lodash.__, args: ReadonlyArray<any>, collection: object | null | undefined): LodashInvokeArgsMap1x6;
        (methodName: string, args: ReadonlyArray<any>, collection: object | null | undefined): any[];
        <TResult>(method: (...args: any[]) => TResult): LodashInvokeArgsMap2x1<TResult>;
        <TResult>(method: (...args: any[]) => TResult, args: ReadonlyArray<any>): LodashInvokeArgsMap2x3<TResult>;
        <TResult>(method: (...args: any[]) => TResult, args: lodash.__, collection: object | null | undefined): LodashInvokeArgsMap2x5<TResult>;
        <TResult>(method: (...args: any[]) => TResult, args: ReadonlyArray<any>, collection: object | null | undefined): TResult[];
    }
    interface LodashInvokeArgsMap1x1 {
        (args: ReadonlyArray<any>): LodashInvokeArgsMap1x3;
        (args: lodash.__, collection: object | null | undefined): LodashInvokeArgsMap1x5;
        (args: ReadonlyArray<any>, collection: object | null | undefined): any[];
    }
    interface LodashInvokeArgsMap1x2 {
        (methodName: string): LodashInvokeArgsMap1x3;
        (methodNameOrMethod: lodash.__, collection: object | null | undefined): LodashInvokeArgsMap1x6;
        (methodName: string, collection: object | null | undefined): any[];
        <TResult>(method: (...args: any[]) => TResult): LodashInvokeArgsMap2x3<TResult>;
        <TResult>(method: (...args: any[]) => TResult, collection: object | null | undefined): TResult[];
    }
    type LodashInvokeArgsMap1x3 = (collection: object | null | undefined) => any[];
    interface LodashInvokeArgsMap1x4 {
        (methodName: string): LodashInvokeArgsMap1x5;
        (methodNameOrMethod: lodash.__, args: ReadonlyArray<any>): LodashInvokeArgsMap1x6;
        (methodName: string, args: ReadonlyArray<any>): any[];
        <TResult>(method: (...args: any[]) => TResult): LodashInvokeArgsMap2x5<TResult>;
        <TResult>(method: (...args: any[]) => TResult, args: ReadonlyArray<any>): TResult[];
    }
    type LodashInvokeArgsMap1x5 = (args: ReadonlyArray<any>) => any[];
    interface LodashInvokeArgsMap1x6 {
        (methodName: string): any[];
        <TResult>(method: (...args: any[]) => TResult): TResult[];
    }
    interface LodashInvokeArgsMap2x1<TResult> {
        (args: ReadonlyArray<any>): LodashInvokeArgsMap2x3<TResult>;
        (args: lodash.__, collection: object | null | undefined): LodashInvokeArgsMap2x5<TResult>;
        (args: ReadonlyArray<any>, collection: object | null | undefined): TResult[];
    }
    type LodashInvokeArgsMap2x3<TResult> = (collection: object | null | undefined) => TResult[];
    type LodashInvokeArgsMap2x5<TResult> = (args: ReadonlyArray<any>) => TResult[];
    interface LodashInvokeMap {
        (methodName: string): LodashInvokeMap1x1;
        (methodNameOrMethod: lodash.__, collection: object | null | undefined): LodashInvokeMap1x2;
        (methodName: string, collection: object | null | undefined): any[];
        <TResult>(method: (...args: any[]) => TResult): LodashInvokeMap2x1<TResult>;
        <TResult>(method: (...args: any[]) => TResult, collection: object | null | undefined): TResult[];
    }
    type LodashInvokeMap1x1 = (collection: object | null | undefined) => any[];
    interface LodashInvokeMap1x2 {
        (methodName: string): any[];
        <TResult>(method: (...args: any[]) => TResult): TResult[];
    }
    type LodashInvokeMap2x1<TResult> = (collection: object | null | undefined) => TResult[];
    type LodashIsArguments = (value: any) => value is IArguments;
    type LodashIsArray = (value: any) => value is any[];
    type LodashIsArrayBuffer = (value: any) => value is ArrayBuffer;
    interface LodashIsArrayLike {
        <T extends { __lodashAnyHack: any }>(t: T): boolean;
        (value: ((...args: any[]) => any) | null | undefined): value is never;
        (value: any): value is { length: number };
    }
    interface LodashIsArrayLikeObject {
        <T extends { __lodashAnyHack: any }>(value: T): boolean;
        (value: ((...args: any[]) => any) | lodash.FunctionBase | string | boolean | number | null | undefined): value is never;
        (value: any): value is object & { length: number };
    }
    type LodashIsBoolean = (value: any) => value is boolean;
    type LodashIsBuffer = (value: any) => boolean;
    type LodashIsDate = (value: any) => value is Date;
    type LodashIsElement = (value: any) => boolean;
    type LodashIsEmpty = (value: any) => boolean;
    interface LodashIsEqualWith {
        (customizer: lodash.IsEqualCustomizer): LodashIsEqualWith1x1;
        (customizer: lodash.__, value: any): LodashIsEqualWith1x2;
        (customizer: lodash.IsEqualCustomizer, value: any): LodashIsEqualWith1x3;
        (customizer: lodash.__, value: lodash.__, other: any): LodashIsEqualWith1x4;
        (customizer: lodash.IsEqualCustomizer, value: lodash.__, other: any): LodashIsEqualWith1x5;
        (customizer: lodash.__, value: any, other: any): LodashIsEqualWith1x6;
        (customizer: lodash.IsEqualCustomizer, value: any, other: any): boolean;
    }
    interface LodashIsEqualWith1x1 {
        (value: any): LodashIsEqualWith1x3;
        (value: lodash.__, other: any): LodashIsEqualWith1x5;
        (value: any, other: any): boolean;
    }
    interface LodashIsEqualWith1x2 {
        (customizer: lodash.IsEqualCustomizer): LodashIsEqualWith1x3;
        (customizer: lodash.__, other: any): LodashIsEqualWith1x6;
        (customizer: lodash.IsEqualCustomizer, other: any): boolean;
    }
    type LodashIsEqualWith1x3 = (other: any) => boolean;
    interface LodashIsEqualWith1x4 {
        (customizer: lodash.IsEqualCustomizer): LodashIsEqualWith1x5;
        (customizer: lodash.__, value: any): LodashIsEqualWith1x6;
        (customizer: lodash.IsEqualCustomizer, value: any): boolean;
    }
    type LodashIsEqualWith1x5 = (value: any) => boolean;
    type LodashIsEqualWith1x6 = (customizer: lodash.IsEqualCustomizer) => boolean;
    type LodashIsError = (value: any) => value is Error;
    type LodashIsFinite = (value: any) => boolean;
    type LodashIsFunction = (value: any) => value is (...args: any[]) => any;
    type LodashIsInteger = (value: any) => boolean;
    type LodashIsLength = (value: any) => boolean;
    type LodashIsMap = (value: any) => value is Map<any, any>;
    interface LodashIsMatch {
        (source: object): LodashIsMatch1x1;
        (source: lodash.__, object: object): LodashIsMatch1x2;
        (source: object, object: object): boolean;
    }
    type LodashIsMatch1x1 = (object: object) => boolean;
    type LodashIsMatch1x2 = (source: object) => boolean;
    interface LodashIsMatchWith {
        (customizer: lodash.isMatchWithCustomizer): LodashIsMatchWith1x1;
        (customizer: lodash.__, source: object): LodashIsMatchWith1x2;
        (customizer: lodash.isMatchWithCustomizer, source: object): LodashIsMatchWith1x3;
        (customizer: lodash.__, source: lodash.__, object: object): LodashIsMatchWith1x4;
        (customizer: lodash.isMatchWithCustomizer, source: lodash.__, object: object): LodashIsMatchWith1x5;
        (customizer: lodash.__, source: object, object: object): LodashIsMatchWith1x6;
        (customizer: lodash.isMatchWithCustomizer, source: object, object: object): boolean;
    }
    interface LodashIsMatchWith1x1 {
        (source: object): LodashIsMatchWith1x3;
        (source: lodash.__, object: object): LodashIsMatchWith1x5;
        (source: object, object: object): boolean;
    }
    interface LodashIsMatchWith1x2 {
        (customizer: lodash.isMatchWithCustomizer): LodashIsMatchWith1x3;
        (customizer: lodash.__, object: object): LodashIsMatchWith1x6;
        (customizer: lodash.isMatchWithCustomizer, object: object): boolean;
    }
    type LodashIsMatchWith1x3 = (object: object) => boolean;
    interface LodashIsMatchWith1x4 {
        (customizer: lodash.isMatchWithCustomizer): LodashIsMatchWith1x5;
        (customizer: lodash.__, source: object): LodashIsMatchWith1x6;
        (customizer: lodash.isMatchWithCustomizer, source: object): boolean;
    }
    type LodashIsMatchWith1x5 = (source: object) => boolean;
    type LodashIsMatchWith1x6 = (customizer: lodash.isMatchWithCustomizer) => boolean;
    type LodashIsNaN = (value: any) => boolean;
    type LodashIsNative = (value: any) => value is (...args: any[]) => any;
    type LodashIsNil = (value: any) => value is null | undefined;
    type LodashIsNull = (value: any) => value is null;
    type LodashIsNumber = (value: any) => value is number;
    type LodashIsObject = (value: any) => value is object;
    type LodashIsObjectLike = (value: any) => boolean;
    type LodashIsPlainObject = (value: any) => boolean;
    type LodashIsRegExp = (value: any) => value is RegExp;
    type LodashIsSafeInteger = (value: any) => boolean;
    type LodashIsSet = (value: any) => value is Set<any>;
    type LodashIsString = (value: any) => value is string;
    type LodashIsSymbol = (value: any) => value is symbol;
    type LodashIsTypedArray = (value: any) => boolean;
    type LodashIsUndefined = (value: any) => value is undefined;
    type LodashIsWeakMap = (value: any) => value is WeakMap<object, any>;
    type LodashIsWeakSet = (value: any) => value is WeakSet<object>;
    interface LodashIteratee {
        <TFunction extends (...args: any[]) => any>(func: TFunction): TFunction;
        (func: string | object): (...args: any[]) => any;
    }
    interface LodashJoin {
        (separator: string): LodashJoin1x1;
        (separator: lodash.__, array: lodash.List<any> | null | undefined): LodashJoin1x2;
        (separator: string, array: lodash.List<any> | null | undefined): string;
    }
    type LodashJoin1x1 = (array: lodash.List<any> | null | undefined) => string;
    type LodashJoin1x2 = (separator: string) => string;
    type LodashOver = <TResult>(iteratees: lodash.Many<(...args: any[]) => TResult>) => (...args: any[]) => TResult[];
    type LodashKebabCase = (string: string) => string;
    type LodashKeys = (object: any) => string[];
    type LodashKeysIn = (object: any) => string[];
    type LodashLast = <T>(array: lodash.List<T> | null | undefined) => T | undefined;
    interface LodashLastIndexOf {
        <T>(value: T): LodashLastIndexOf1x1<T>;
        <T>(value: lodash.__, array: lodash.List<T> | null | undefined): LodashLastIndexOf1x2<T>;
        <T>(value: T, array: lodash.List<T> | null | undefined): number;
    }
    type LodashLastIndexOf1x1<T> = (array: lodash.List<T> | null | undefined) => number;
    type LodashLastIndexOf1x2<T> = (value: T) => number;
    interface LodashLastIndexOfFrom {
        <T>(value: T): LodashLastIndexOfFrom1x1<T>;
        (value: lodash.__, fromIndex: true|number): LodashLastIndexOfFrom1x2;
        <T>(value: T, fromIndex: true|number): LodashLastIndexOfFrom1x3<T>;
        <T>(value: lodash.__, fromIndex: lodash.__, array: lodash.List<T> | null | undefined): LodashLastIndexOfFrom1x4<T>;
        <T>(value: T, fromIndex: lodash.__, array: lodash.List<T> | null | undefined): LodashLastIndexOfFrom1x5;
        <T>(value: lodash.__, fromIndex: true|number, array: lodash.List<T> | null | undefined): LodashLastIndexOfFrom1x6<T>;
        <T>(value: T, fromIndex: true|number, array: lodash.List<T> | null | undefined): number;
    }
    interface LodashLastIndexOfFrom1x1<T> {
        (fromIndex: true|number): LodashLastIndexOfFrom1x3<T>;
        (fromIndex: lodash.__, array: lodash.List<T> | null | undefined): LodashLastIndexOfFrom1x5;
        (fromIndex: true|number, array: lodash.List<T> | null | undefined): number;
    }
    interface LodashLastIndexOfFrom1x2 {
        <T>(value: T): LodashLastIndexOfFrom1x3<T>;
        <T>(value: lodash.__, array: lodash.List<T> | null | undefined): LodashLastIndexOfFrom1x6<T>;
        <T>(value: T, array: lodash.List<T> | null | undefined): number;
    }
    type LodashLastIndexOfFrom1x3<T> = (array: lodash.List<T> | null | undefined) => number;
    interface LodashLastIndexOfFrom1x4<T> {
        (value: T): LodashLastIndexOfFrom1x5;
        (value: lodash.__, fromIndex: true|number): LodashLastIndexOfFrom1x6<T>;
        (value: T, fromIndex: true|number): number;
    }
    type LodashLastIndexOfFrom1x5 = (fromIndex: true|number) => number;
    type LodashLastIndexOfFrom1x6<T> = (value: T) => number;
    type LodashLowerCase = (string: string) => string;
    type LodashLowerFirst = (string: string) => string;
    interface LodashLt {
        (value: any): LodashLt1x1;
        (value: lodash.__, other: any): LodashLt1x2;
        (value: any, other: any): boolean;
    }
    type LodashLt1x1 = (other: any) => boolean;
    type LodashLt1x2 = (value: any) => boolean;
    interface LodashLte {
        (value: any): LodashLte1x1;
        (value: lodash.__, other: any): LodashLte1x2;
        (value: any, other: any): boolean;
    }
    type LodashLte1x1 = (other: any) => boolean;
    type LodashLte1x2 = (value: any) => boolean;
    interface LodashMap {
        <T, TResult>(iteratee: (value: T) => TResult): LodashMap1x1<T, TResult>;
        <T>(iteratee: lodash.__, collection: T[] | null | undefined): LodashMap1x2<T>;
        <T, TResult>(iteratee: (value: T) => TResult, collection: T[] | lodash.List<T> | null | undefined): TResult[];
        <T>(iteratee: lodash.__, collection: lodash.List<T> | null | undefined): LodashMap2x2<T>;
        <T extends object, TResult>(iteratee: (value: T[keyof T]) => TResult): LodashMap3x1<T, TResult>;
        <T extends object>(iteratee: lodash.__, collection: T | null | undefined): LodashMap3x2<T>;
        <T extends object, TResult>(iteratee: (value: T[keyof T]) => TResult, collection: T | null | undefined): TResult[];
        <T, K extends keyof T>(iteratee: K): LodashMap4x1<T, K>;
        <T>(iteratee: lodash.__, collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): LodashMap4x2<T>;
        <T, K extends keyof T>(iteratee: K, collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): Array<T[K]>;
        (iteratee: string): LodashMap5x1;
        <T>(iteratee: string, collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): any[];
        (iteratee: object): LodashMap6x1;
        <T>(iteratee: object, collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): boolean[];
    }
    type LodashMap1x1<T, TResult> = (collection: T[] | lodash.List<T> | null | undefined) => TResult[];
    type LodashMap1x2<T> = <TResult>(iteratee: (value: T) => TResult) => TResult[];
    type LodashMap2x2<T> = <TResult>(iteratee: (value: T) => TResult) => TResult[];
    type LodashMap3x1<T, TResult> = (collection: T | null | undefined) => TResult[];
    type LodashMap3x2<T> = <TResult>(iteratee: (value: T[keyof T]) => TResult) => TResult[];
    type LodashMap4x1<T, K extends keyof T> = (collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined) => Array<T[K]>;
    interface LodashMap4x2<T> {
        <K extends keyof T>(iteratee: K): Array<T[K]>;
        (iteratee: string): any[];
        (iteratee: object): boolean[];
    }
    type LodashMap5x1 = <T>(collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined) => any[];
    type LodashMap6x1 = <T>(collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined) => boolean[];
    interface LodashMapKeys {
        (iteratee: lodash.ValueIteratee<number>): LodashMapKeys1x1;
        <T>(iteratee: lodash.__, object: lodash.List<T> | null | undefined): LodashMapKeys1x2<T>;
        <T>(iteratee: lodash.ValueIteratee<number>, object: lodash.List<T> | null | undefined): lodash.Dictionary<T>;
        (iteratee: lodash.ValueIteratee<string>): LodashMapKeys2x1;
        <T extends object>(iteratee: lodash.__, object: T | null | undefined): LodashMapKeys2x2<T>;
        <T extends object>(iteratee: lodash.ValueIteratee<string>, object: T | null | undefined): lodash.Dictionary<T[keyof T]>;
    }
    type LodashMapKeys1x1 = <T>(object: lodash.List<T> | null | undefined) => lodash.Dictionary<T>;
    type LodashMapKeys1x2<T> = (iteratee: lodash.ValueIteratee<number>) => lodash.Dictionary<T>;
    type LodashMapKeys2x1 = <T extends object>(object: T | null | undefined) => lodash.Dictionary<T[keyof T]>;
    type LodashMapKeys2x2<T> = (iteratee: lodash.ValueIteratee<string>) => lodash.Dictionary<T[keyof T]>;
    interface LodashMapValues {
        <T, TResult>(callback: (value: T) => TResult): LodashMapValues1x1<T, TResult>;
        <T>(callbackOrIterateeOrIterateeOrIteratee: lodash.__, obj: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): LodashMapValues1x2<T>;
        <T, TResult>(callback: (value: T) => TResult, obj: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): lodash.Dictionary<TResult>;
        <T extends object, TResult>(callback: (value: T[keyof T]) => TResult): LodashMapValues2x1<T, TResult>;
        <T extends object>(callbackOrIterateeOrIteratee: lodash.__, obj: T | null | undefined): LodashMapValues2x2<T>;
        <T extends object, TResult>(callback: (value: T[keyof T]) => TResult, obj: T | null | undefined): { [P in keyof T]: TResult };
        (iteratee: object): LodashMapValues3x1;
        <T>(iteratee: object, obj: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): lodash.Dictionary<boolean>;
        <T extends object>(iteratee: object, obj: T | null | undefined): { [P in keyof T]: boolean };
        <T, TKey extends keyof T>(iteratee: TKey): LodashMapValues5x1<T, TKey>;
        <T, TKey extends keyof T>(iteratee: TKey, obj: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): lodash.Dictionary<T[TKey]>;
        (iteratee: string): LodashMapValues6x1;
        <T>(iteratee: string, obj: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): lodash.Dictionary<any>;
        <T extends object>(iteratee: string, obj: T | null | undefined): { [P in keyof T]: any };
    }
    type LodashMapValues1x1<T, TResult> = (obj: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined) => lodash.Dictionary<TResult>;
    interface LodashMapValues1x2<T> {
        <TResult>(callback: (value: T) => TResult): lodash.Dictionary<TResult>;
        (iteratee: object): lodash.Dictionary<boolean>;
        <TKey extends keyof T>(iteratee: TKey): lodash.Dictionary<T[TKey]>;
        (iteratee: string): lodash.Dictionary<any>;
    }
    type LodashMapValues2x1<T, TResult> = (obj: T | null | undefined) => { [P in keyof T]: TResult };
    interface LodashMapValues2x2<T> {
        <TResult>(callback: (value: T[keyof T]) => TResult): { [P in keyof T]: TResult };
        (iteratee: object): { [P in keyof T]: boolean };
        (iteratee: string): { [P in keyof T]: any };
    }
    interface LodashMapValues3x1 {
        <T>(obj: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): lodash.Dictionary<boolean>;
        <T extends object>(obj: T | null | undefined): { [P in keyof T]: boolean };
    }
    type LodashMapValues5x1<T, TKey extends keyof T> = (obj: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined) => lodash.Dictionary<T[TKey]>;
    interface LodashMapValues6x1 {
        <T>(obj: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): lodash.Dictionary<any>;
        <T extends object>(obj: T | null | undefined): { [P in keyof T]: any };
    }
    interface LodashMatchesProperty {
        (path: lodash.PropertyPath): LodashMatchesProperty1x1;
        <T>(path: lodash.__, srcValue: T): LodashMatchesProperty1x2;
        <T>(path: lodash.PropertyPath, srcValue: T): (value: any) => boolean;
    }
    type LodashMatchesProperty1x1 = <T>(srcValue: T) => (value: any) => boolean;
    type LodashMatchesProperty1x2 = (path: lodash.PropertyPath) => (value: any) => boolean;
    type LodashMax = <T>(collection: lodash.List<T> | null | undefined) => T | undefined;
    interface LodashMaxBy {
        <T>(iteratee: lodash.ValueIteratee<T>): LodashMaxBy1x1<T>;
        <T>(iteratee: lodash.__, collection: lodash.List<T> | null | undefined): LodashMaxBy1x2<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, collection: lodash.List<T> | null | undefined): T | undefined;
    }
    type LodashMaxBy1x1<T> = (collection: lodash.List<T> | null | undefined) => T | undefined;
    type LodashMaxBy1x2<T> = (iteratee: lodash.ValueIteratee<T>) => T | undefined;
    type LodashMean = (collection: lodash.List<any> | null | undefined) => number;
    interface LodashMeanBy {
        <T>(iteratee: lodash.ValueIteratee<T>): LodashMeanBy1x1<T>;
        <T>(iteratee: lodash.__, collection: lodash.List<T> | null | undefined): LodashMeanBy1x2<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, collection: lodash.List<T> | null | undefined): number;
    }
    type LodashMeanBy1x1<T> = (collection: lodash.List<T> | null | undefined) => number;
    type LodashMeanBy1x2<T> = (iteratee: lodash.ValueIteratee<T>) => number;
    type LodashMemoize = <T extends (...args: any) => any>(func: T) => T & lodash.MemoizedFunction;
    interface LodashMerge {
        <TObject>(object: TObject): LodashMerge1x1<TObject>;
        <TSource>(object: lodash.__, source: TSource): LodashMerge1x2<TSource>;
        <TObject, TSource>(object: TObject, source: TSource): TObject & TSource;
    }
    type LodashMerge1x1<TObject> = <TSource>(source: TSource) => TObject & TSource;
    type LodashMerge1x2<TSource> = <TObject>(object: TObject) => TObject & TSource;
    interface LodashMergeAll {
        <TObject, TSource>(object: [TObject, TSource]): TObject & TSource;
        <TObject, TSource1, TSource2>(object: [TObject, TSource1, TSource2]): TObject & TSource1 & TSource2;
        <TObject, TSource1, TSource2, TSource3>(object: [TObject, TSource1, TSource2, TSource3]): TObject & TSource1 & TSource2 & TSource3;
        <TObject, TSource1, TSource2, TSource3, TSource4>(object: [TObject, TSource1, TSource2, TSource3, TSource4]): TObject & TSource1 & TSource2 & TSource3 & TSource4;
        (object: ReadonlyArray<any>): any;
    }
    interface LodashMergeAllWith {
        (customizer: lodash.MergeWithCustomizer): LodashMergeAllWith1x1;
        (customizer: lodash.__, args: ReadonlyArray<any>): LodashMergeAllWith1x2;
        (customizer: lodash.MergeWithCustomizer, args: ReadonlyArray<any>): any;
    }
    type LodashMergeAllWith1x1 = (args: ReadonlyArray<any>) => any;
    type LodashMergeAllWith1x2 = (customizer: lodash.MergeWithCustomizer) => any;
    interface LodashMergeWith {
        (customizer: lodash.MergeWithCustomizer): LodashMergeWith1x1;
        <TObject>(customizer: lodash.__, object: TObject): LodashMergeWith1x2<TObject>;
        <TObject>(customizer: lodash.MergeWithCustomizer, object: TObject): LodashMergeWith1x3<TObject>;
        <TSource>(customizer: lodash.__, object: lodash.__, source: TSource): LodashMergeWith1x4<TSource>;
        <TSource>(customizer: lodash.MergeWithCustomizer, object: lodash.__, source: TSource): LodashMergeWith1x5<TSource>;
        <TObject, TSource>(customizer: lodash.__, object: TObject, source: TSource): LodashMergeWith1x6<TObject, TSource>;
        <TObject, TSource>(customizer: lodash.MergeWithCustomizer, object: TObject, source: TSource): TObject & TSource;
    }
    interface LodashMergeWith1x1 {
        <TObject>(object: TObject): LodashMergeWith1x3<TObject>;
        <TSource>(object: lodash.__, source: TSource): LodashMergeWith1x5<TSource>;
        <TObject, TSource>(object: TObject, source: TSource): TObject & TSource;
    }
    interface LodashMergeWith1x2<TObject> {
        (customizer: lodash.MergeWithCustomizer): LodashMergeWith1x3<TObject>;
        <TSource>(customizer: lodash.__, source: TSource): LodashMergeWith1x6<TObject, TSource>;
        <TSource>(customizer: lodash.MergeWithCustomizer, source: TSource): TObject & TSource;
    }
    type LodashMergeWith1x3<TObject> = <TSource>(source: TSource) => TObject & TSource;
    interface LodashMergeWith1x4<TSource> {
        (customizer: lodash.MergeWithCustomizer): LodashMergeWith1x5<TSource>;
        <TObject>(customizer: lodash.__, object: TObject): LodashMergeWith1x6<TObject, TSource>;
        <TObject>(customizer: lodash.MergeWithCustomizer, object: TObject): TObject & TSource;
    }
    type LodashMergeWith1x5<TSource> = <TObject>(object: TObject) => TObject & TSource;
    type LodashMergeWith1x6<TObject, TSource> = (customizer: lodash.MergeWithCustomizer) => TObject & TSource;
    type LodashMethod = (path: lodash.PropertyPath) => (object: any) => any;
    type LodashMethodOf = (object: object) => (path: lodash.PropertyPath) => any;
    type LodashMin = <T>(collection: lodash.List<T> | null | undefined) => T | undefined;
    interface LodashMinBy {
        <T>(iteratee: lodash.ValueIteratee<T>): LodashMinBy1x1<T>;
        <T>(iteratee: lodash.__, collection: lodash.List<T> | null | undefined): LodashMinBy1x2<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, collection: lodash.List<T> | null | undefined): T | undefined;
    }
    type LodashMinBy1x1<T> = (collection: lodash.List<T> | null | undefined) => T | undefined;
    type LodashMinBy1x2<T> = (iteratee: lodash.ValueIteratee<T>) => T | undefined;
    interface LodashMultiply {
        (multiplier: number): LodashMultiply1x1;
        (multiplier: lodash.__, multiplicand: number): LodashMultiply1x2;
        (multiplier: number, multiplicand: number): number;
    }
    type LodashMultiply1x1 = (multiplicand: number) => number;
    type LodashMultiply1x2 = (multiplier: number) => number;
    type LodashNoConflict = () => typeof _;
    type LodashNoop = (...args: any[]) => void;
    type LodashNow = () => number;
    interface LodashNth {
        (n: number): LodashNth1x1;
        <T>(n: lodash.__, array: lodash.List<T> | null | undefined): LodashNth1x2<T>;
        <T>(n: number, array: lodash.List<T> | null | undefined): T | undefined;
    }
    type LodashNth1x1 = <T>(array: lodash.List<T> | null | undefined) => T | undefined;
    type LodashNth1x2<T> = (n: number) => T | undefined;
    type LodashNthArg = (n: number) => (...args: any[]) => any;
    interface LodashOmit {
        <T extends object, K extends keyof T>(paths: lodash.Many<K>): LodashOmit1x1<T, K>;
        <T extends object>(paths: lodash.__, object: T | null | undefined): LodashOmit1x2<T>;
        <T extends object, K extends keyof T>(paths: lodash.Many<K>, object: T | null | undefined): lodash.Omit<T, K>;
        (paths: lodash.Many<lodash.PropertyName>): LodashOmit2x1;
        <T extends object>(paths: lodash.Many<lodash.PropertyName>, object: T | null | undefined): lodash.PartialObject<T>;
    }
    type LodashOmit1x1<T, K extends keyof T> = (object: T | null | undefined) => lodash.Omit<T, K>;
    interface LodashOmit1x2<T> {
        <K extends keyof T>(paths: lodash.Many<K>): lodash.Omit<T, K>;
        (paths: lodash.Many<lodash.PropertyName>): lodash.PartialObject<T>;
    }
    type LodashOmit2x1 = <T extends object>(object: T | null | undefined) => lodash.PartialObject<T>;
    interface LodashOmitBy {
        <T>(predicate: lodash.ValueKeyIteratee<T>): LodashOmitBy1x1<T>;
        <T>(predicate: lodash.__, object: lodash.Dictionary<T> | null | undefined): LodashOmitBy1x2<T>;
        <T>(predicate: lodash.ValueKeyIteratee<T>, object: lodash.Dictionary<T> | null | undefined): lodash.Dictionary<T>;
        <T>(predicate: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashOmitBy2x2<T>;
        <T>(predicate: lodash.ValueKeyIteratee<T>, object: lodash.NumericDictionary<T> | null | undefined): lodash.NumericDictionary<T>;
        <T extends object>(predicate: lodash.__, object: T | null | undefined): LodashOmitBy3x2<T>;
        <T extends object>(predicate: lodash.ValueKeyIteratee<T[keyof T]>, object: T | null | undefined): lodash.PartialObject<T>;
    }
    interface LodashOmitBy1x1<T> {
        (object: lodash.Dictionary<T> | null | undefined): lodash.Dictionary<T>;
        (object: lodash.NumericDictionary<T> | null | undefined): lodash.NumericDictionary<T>;
        <T1 extends object>(object: T1 | null | undefined): lodash.PartialObject<T1>;
    }
    type LodashOmitBy1x2<T> = (predicate: lodash.ValueKeyIteratee<T>) => lodash.Dictionary<T>;
    type LodashOmitBy2x2<T> = (predicate: lodash.ValueKeyIteratee<T>) => lodash.NumericDictionary<T>;
    type LodashOmitBy3x2<T> = (predicate: lodash.ValueKeyIteratee<T[keyof T]>) => lodash.PartialObject<T>;
    type LodashOnce = <T extends (...args: any) => any>(func: T) => T;
    interface LodashOrderBy {
        <T>(iteratees: lodash.Many<(value: T) => lodash.NotVoid>): LodashOrderBy1x1<T>;
        (iteratees: lodash.__, orders: lodash.Many<boolean|"asc"|"desc">): LodashOrderBy1x2;
        <T>(iteratees: lodash.Many<(value: T) => lodash.NotVoid>, orders: lodash.Many<boolean|"asc"|"desc">): LodashOrderBy1x3<T>;
        <T>(iteratees: lodash.__, orders: lodash.__, collection: lodash.List<T> | null | undefined): LodashOrderBy1x4<T>;
        <T>(iteratees: lodash.Many<(value: T) => lodash.NotVoid>, orders: lodash.__, collection: lodash.List<T> | null | undefined): LodashOrderBy1x5<T>;
        <T>(iteratees: lodash.__, orders: lodash.Many<boolean|"asc"|"desc">, collection: lodash.List<T> | null | undefined): LodashOrderBy1x6<T>;
        <T>(iteratees: lodash.Many<(value: T) => lodash.NotVoid> | lodash.Many<lodash.ValueIteratee<T>>, orders: lodash.Many<boolean | "asc" | "desc">, collection: lodash.List<T> | null | undefined): T[];
        <T>(iteratees: lodash.Many<lodash.ValueIteratee<T>>): LodashOrderBy2x1<T>;
        <T>(iteratees: lodash.Many<lodash.ValueIteratee<T>>, orders: lodash.Many<boolean|"asc"|"desc">): LodashOrderBy2x3<T>;
        <T>(iteratees: lodash.Many<lodash.ValueIteratee<T>>, orders: lodash.__, collection: lodash.List<T> | null | undefined): LodashOrderBy2x5<T>;
        <T extends object>(iteratees: lodash.__, orders: lodash.__, collection: T | null | undefined): LodashOrderBy3x4<T>;
        <T extends object>(iteratees: lodash.Many<(value: T[keyof T]) => lodash.NotVoid>, orders: lodash.__, collection: T | null | undefined): LodashOrderBy3x5<T>;
        <T extends object>(iteratees: lodash.__, orders: lodash.Many<boolean|"asc"|"desc">, collection: T | null | undefined): LodashOrderBy3x6<T>;
        <T extends object>(iteratees: lodash.Many<(value: T[keyof T]) => lodash.NotVoid> | lodash.Many<lodash.ValueIteratee<T[keyof T]>>, orders: lodash.Many<boolean | "asc" | "desc">, collection: T | null | undefined): Array<T[keyof T]>;
        <T extends object>(iteratees: lodash.Many<lodash.ValueIteratee<T[keyof T]>>, orders: lodash.__, collection: T | null | undefined): LodashOrderBy4x5<T>;
    }
    interface LodashOrderBy1x1<T> {
        (orders: lodash.Many<boolean | "asc" | "desc">): LodashOrderBy1x3<T>;
        (orders: lodash.__, collection: lodash.List<T> | null | undefined): LodashOrderBy1x5<T>;
        (orders: lodash.Many<boolean | "asc" | "desc">, collection: lodash.List<T> | object | null | undefined): T[];
        <T1 extends object>(orders: lodash.__, collection: T1 | null | undefined): LodashOrderBy3x5<T>;
    }
    interface LodashOrderBy1x2 {
        <T>(iteratees: lodash.Many<(value: T) => lodash.NotVoid>): LodashOrderBy1x3<T>;
        <T>(iteratees: lodash.__, collection: lodash.List<T> | null | undefined): LodashOrderBy1x6<T>;
        <T>(iteratees: lodash.Many<(value: T) => lodash.NotVoid> | lodash.Many<lodash.ValueIteratee<T>>, collection: lodash.List<T> | null | undefined): T[];
        <T>(iteratees: lodash.Many<lodash.ValueIteratee<T>>): LodashOrderBy2x3<T>;
        <T extends object>(iteratees: lodash.__, collection: T | null | undefined): LodashOrderBy3x6<T>;
        <T extends object>(iteratees: lodash.Many<(value: T[keyof T]) => lodash.NotVoid> | lodash.Many<lodash.ValueIteratee<T[keyof T]>>, collection: T | null | undefined): Array<T[keyof T]>;
    }
    interface LodashOrderBy1x3<T> {
        (collection: lodash.List<T> | null | undefined): T[];
        (collection: object | null | undefined): object[];
    }
    interface LodashOrderBy1x4<T> {
        (iteratees: lodash.Many<(value: T) => lodash.NotVoid>): LodashOrderBy1x5<T>;
        (iteratees: lodash.__, orders: lodash.Many<boolean | "asc" | "desc">): LodashOrderBy1x6<T>;
        (iteratees: lodash.Many<(value: T) => lodash.NotVoid> | lodash.Many<lodash.ValueIteratee<T>>, orders: lodash.Many<boolean | "asc" | "desc">): T[];
        (iteratees: lodash.Many<lodash.ValueIteratee<T>>): LodashOrderBy2x5<T>;
    }
    type LodashOrderBy1x5<T> = (orders: lodash.Many<boolean|"asc"|"desc">) => T[];
    type LodashOrderBy1x6<T> = (iteratees: lodash.Many<(value: T) => lodash.NotVoid> | lodash.Many<lodash.ValueIteratee<T>>) => T[];
    interface LodashOrderBy2x1<T> {
        (orders: lodash.Many<boolean | "asc" | "desc">): LodashOrderBy2x3<T>;
        (orders: lodash.__, collection: lodash.List<T> | null | undefined): LodashOrderBy2x5<T>;
        (orders: lodash.Many<boolean | "asc" | "desc">, collection: lodash.List<T> | object | null | undefined): T[];
        <T1 extends object>(orders: lodash.__, collection: T1 | null | undefined): LodashOrderBy4x5<T>;
    }
    interface LodashOrderBy2x3<T> {
        (collection: lodash.List<T> | null | undefined): T[];
        (collection: object | null | undefined): object[];
    }
    type LodashOrderBy2x5<T> = (orders: lodash.Many<boolean|"asc"|"desc">) => T[];
    interface LodashOrderBy3x4<T> {
        (iteratees: lodash.Many<(value: T[keyof T]) => lodash.NotVoid>): LodashOrderBy3x5<T>;
        (iteratees: lodash.__, orders: lodash.Many<boolean | "asc" | "desc">): LodashOrderBy3x6<T>;
        (iteratees: lodash.Many<(value: T[keyof T]) => lodash.NotVoid> | lodash.Many<lodash.ValueIteratee<T[keyof T]>>, orders: lodash.Many<boolean | "asc" | "desc">): Array<T[keyof T]>;
        (iteratees: lodash.Many<lodash.ValueIteratee<T[keyof T]>>): LodashOrderBy4x5<T>;
    }
    type LodashOrderBy3x5<T> = (orders: lodash.Many<boolean|"asc"|"desc">) => Array<T[keyof T]>;
    type LodashOrderBy3x6<T> = (iteratees: lodash.Many<(value: T[keyof T]) => lodash.NotVoid> | lodash.Many<lodash.ValueIteratee<T[keyof T]>>) => Array<T[keyof T]>;
    type LodashOrderBy4x5<T> = (orders: lodash.Many<boolean|"asc"|"desc">) => Array<T[keyof T]>;
    interface LodashOverArgs {
        (func: (...args: any[]) => any): LodashOverArgs1x1;
        (func: lodash.__, transforms: lodash.Many<(...args: any[]) => any>): LodashOverArgs1x2;
        (func: (...args: any[]) => any, transforms: lodash.Many<(...args: any[]) => any>): (...args: any[]) => any;
    }
    type LodashOverArgs1x1 = (transforms: lodash.Many<(...args: any[]) => any>) => (...args: any[]) => any;
    type LodashOverArgs1x2 = (func: (...args: any[]) => any) => (...args: any[]) => any;
    interface LodashPad {
        (length: number): LodashPad1x1;
        (length: lodash.__, string: string): LodashPad1x2;
        (length: number, string: string): string;
    }
    type LodashPad1x1 = (string: string) => string;
    type LodashPad1x2 = (length: number) => string;
    interface LodashPadChars {
        (chars: string): LodashPadChars1x1;
        (chars: lodash.__, length: number): LodashPadChars1x2;
        (chars: string, length: number): LodashPadChars1x3;
        (chars: lodash.__, length: lodash.__, string: string): LodashPadChars1x4;
        (chars: string, length: lodash.__, string: string): LodashPadChars1x5;
        (chars: lodash.__, length: number, string: string): LodashPadChars1x6;
        (chars: string, length: number, string: string): string;
    }
    interface LodashPadChars1x1 {
        (length: number): LodashPadChars1x3;
        (length: lodash.__, string: string): LodashPadChars1x5;
        (length: number, string: string): string;
    }
    interface LodashPadChars1x2 {
        (chars: string): LodashPadChars1x3;
        (chars: lodash.__, string: string): LodashPadChars1x6;
        (chars: string, string: string): string;
    }
    type LodashPadChars1x3 = (string: string) => string;
    interface LodashPadChars1x4 {
        (chars: string): LodashPadChars1x5;
        (chars: lodash.__, length: number): LodashPadChars1x6;
        (chars: string, length: number): string;
    }
    type LodashPadChars1x5 = (length: number) => string;
    type LodashPadChars1x6 = (chars: string) => string;
    interface LodashPadCharsEnd {
        (chars: string): LodashPadCharsEnd1x1;
        (chars: lodash.__, length: number): LodashPadCharsEnd1x2;
        (chars: string, length: number): LodashPadCharsEnd1x3;
        (chars: lodash.__, length: lodash.__, string: string): LodashPadCharsEnd1x4;
        (chars: string, length: lodash.__, string: string): LodashPadCharsEnd1x5;
        (chars: lodash.__, length: number, string: string): LodashPadCharsEnd1x6;
        (chars: string, length: number, string: string): string;
    }
    interface LodashPadCharsEnd1x1 {
        (length: number): LodashPadCharsEnd1x3;
        (length: lodash.__, string: string): LodashPadCharsEnd1x5;
        (length: number, string: string): string;
    }
    interface LodashPadCharsEnd1x2 {
        (chars: string): LodashPadCharsEnd1x3;
        (chars: lodash.__, string: string): LodashPadCharsEnd1x6;
        (chars: string, string: string): string;
    }
    type LodashPadCharsEnd1x3 = (string: string) => string;
    interface LodashPadCharsEnd1x4 {
        (chars: string): LodashPadCharsEnd1x5;
        (chars: lodash.__, length: number): LodashPadCharsEnd1x6;
        (chars: string, length: number): string;
    }
    type LodashPadCharsEnd1x5 = (length: number) => string;
    type LodashPadCharsEnd1x6 = (chars: string) => string;
    interface LodashPadCharsStart {
        (chars: string): LodashPadCharsStart1x1;
        (chars: lodash.__, length: number): LodashPadCharsStart1x2;
        (chars: string, length: number): LodashPadCharsStart1x3;
        (chars: lodash.__, length: lodash.__, string: string): LodashPadCharsStart1x4;
        (chars: string, length: lodash.__, string: string): LodashPadCharsStart1x5;
        (chars: lodash.__, length: number, string: string): LodashPadCharsStart1x6;
        (chars: string, length: number, string: string): string;
    }
    interface LodashPadCharsStart1x1 {
        (length: number): LodashPadCharsStart1x3;
        (length: lodash.__, string: string): LodashPadCharsStart1x5;
        (length: number, string: string): string;
    }
    interface LodashPadCharsStart1x2 {
        (chars: string): LodashPadCharsStart1x3;
        (chars: lodash.__, string: string): LodashPadCharsStart1x6;
        (chars: string, string: string): string;
    }
    type LodashPadCharsStart1x3 = (string: string) => string;
    interface LodashPadCharsStart1x4 {
        (chars: string): LodashPadCharsStart1x5;
        (chars: lodash.__, length: number): LodashPadCharsStart1x6;
        (chars: string, length: number): string;
    }
    type LodashPadCharsStart1x5 = (length: number) => string;
    type LodashPadCharsStart1x6 = (chars: string) => string;
    interface LodashPadEnd {
        (length: number): LodashPadEnd1x1;
        (length: lodash.__, string: string): LodashPadEnd1x2;
        (length: number, string: string): string;
    }
    type LodashPadEnd1x1 = (string: string) => string;
    type LodashPadEnd1x2 = (length: number) => string;
    interface LodashPadStart {
        (length: number): LodashPadStart1x1;
        (length: lodash.__, string: string): LodashPadStart1x2;
        (length: number, string: string): string;
    }
    type LodashPadStart1x1 = (string: string) => string;
    type LodashPadStart1x2 = (length: number) => string;
    interface LodashParseInt {
        (radix: number): LodashParseInt1x1;
        (radix: lodash.__, string: string): LodashParseInt1x2;
        (radix: number, string: string): number;
    }
    type LodashParseInt1x1 = (string: string) => number;
    type LodashParseInt1x2 = (radix: number) => number;
    interface LodashPartial {
        <T1, T2, R>(func: lodash.Function2<T1, T2, R>): LodashPartial1x1<T1, T2, R>;
        <T2>(func: lodash.__, plc1: [lodash.__, T2]): LodashPartial1x2<T2>;
        <T1, T2, R>(func: lodash.Function2<T1, T2, R>, plc1: [lodash.__, T2]): lodash.Function1<T1, R>;
        <T1, T2, T3, R>(func: lodash.Function3<T1, T2, T3, R>): LodashPartial2x1<T1, T2, T3, R>;
        <T1, T2, T3, R>(func: lodash.Function3<T1, T2, T3, R>, plc1: [lodash.__, T2]): lodash.Function2<T1, T3, R>;
        <T3>(func: lodash.__, plc1: [lodash.__, lodash.__, T3]): LodashPartial3x2<T3>;
        <T1, T2, T3, R>(func: lodash.Function3<T1, T2, T3, R>, plc1: [lodash.__, lodash.__, T3]): lodash.Function2<T1, T2, R>;
        <T1, T3>(func: lodash.__, arg1: [T1, lodash.__, T3]): LodashPartial4x2<T1, T3>;
        <T1, T2, T3, R>(func: lodash.Function3<T1, T2, T3, R>, arg1: [T1, lodash.__, T3]): lodash.Function1<T2, R>;
        <T2, T3>(func: lodash.__, plc1: [lodash.__, T2, T3]): LodashPartial5x2<T2, T3>;
        <T1, T2, T3, R>(func: lodash.Function3<T1, T2, T3, R>, plc1: [lodash.__, T2, T3]): lodash.Function1<T1, R>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>): LodashPartial6x1<T1, T2, T3, T4, R>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, plc1: [lodash.__, T2]): lodash.Function3<T1, T3, T4, R>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, plc1: [lodash.__, lodash.__, T3]): lodash.Function3<T1, T2, T4, R>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg1: [T1, lodash.__, T3]): lodash.Function2<T2, T4, R>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, plc1: [lodash.__, T2, T3]): lodash.Function2<T1, T4, R>;
        <T1, T2, T3>(func: lodash.__, arg1OrT1: [T1, T2, T3]): LodashPartial10x2<T1, T2, T3>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg1: [T1, T2, T3]): lodash.Function1<T4, R>;
        <T4>(func: lodash.__, plc1: [lodash.__, lodash.__, lodash.__, T4]): LodashPartial11x2<T4>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, plc1: [lodash.__, lodash.__, lodash.__, T4]): lodash.Function3<T1, T2, T3, R>;
        <T1, T4>(func: lodash.__, arg1: [T1, lodash.__, lodash.__, T4]): LodashPartial12x2<T1, T4>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg1: [T1, lodash.__, lodash.__, T4]): lodash.Function2<T2, T3, R>;
        <T2, T4>(func: lodash.__, plc1: [lodash.__, T2, lodash.__, T4]): LodashPartial13x2<T2, T4>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, plc1: [lodash.__, T2, lodash.__, T4]): lodash.Function2<T1, T3, R>;
        <T1, T2, T4>(func: lodash.__, arg1: [T1, T2, lodash.__, T4]): LodashPartial14x2<T1, T2, T4>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg1: [T1, T2, lodash.__, T4]): lodash.Function1<T3, R>;
        <T3, T4>(func: lodash.__, plc1: [lodash.__, lodash.__, T3, T4]): LodashPartial15x2<T3, T4>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, plc1: [lodash.__, lodash.__, T3, T4]): lodash.Function2<T1, T2, R>;
        <T1, T3, T4>(func: lodash.__, arg1: [T1, lodash.__, T3, T4]): LodashPartial16x2<T1, T3, T4>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg1: [T1, lodash.__, T3, T4]): lodash.Function1<T2, R>;
        <T2, T3, T4>(func: lodash.__, plc1: [lodash.__, T2, T3, T4]): LodashPartial17x2<T2, T3, T4>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, plc1: [lodash.__, T2, T3, T4]): lodash.Function1<T1, R>;
        <TS extends any[], T1, R>(func: (t1: T1, ...ts: TS) => R): LodashPartial18x1<TS, T1, R>;
        <T1>(func: lodash.__, arg1: [T1]): LodashPartial18x2<T1>;
        <TS extends any[], T1, R>(func: (t1: T1, ...ts: TS) => R, arg1: [T1]): (...ts: TS) => R;
        <TS extends any[], T1, T2, R>(func: (t1: T1, t2: T2, ...ts: TS) => R): LodashPartial19x1<TS, T1, T2, R>;
        <T1, T2>(func: lodash.__, t1: [T1, T2]): LodashPartial19x2<T1, T2>;
        <TS extends any[], T1, T2, R>(func: (t1: T1, t2: T2, ...ts: TS) => R, t1: [T1, T2]): (...ts: TS) => R;
        <TS extends any[], T1, T2, T3, R>(func: (t1: T1, t2: T2, t3: T3, ...ts: TS) => R): LodashPartial20x1<TS, T1, T2, T3, R>;
        <TS extends any[], T1, T2, T3, R>(func: (t1: T1, t2: T2, t3: T3, ...ts: TS) => R, t1: [T1, T2, T3]): (...ts: TS) => R;
        <TS extends any[], T1, T2, T3, T4, R>(func: (t1: T1, t2: T2, t3: T3, t4: T4, ...ts: TS) => R): LodashPartial21x1<TS, T1, T2, T3, T4, R>;
        <T1, T2, T3, T4>(func: lodash.__, t1: [T1, T2, T3, T4]): LodashPartial21x2<T1, T2, T3, T4>;
        <TS extends any[], T1, T2, T3, T4, R>(func: (t1: T1, t2: T2, t3: T3, t4: T4, ...ts: TS) => R, t1: [T1, T2, T3, T4]): (...ts: TS) => R;
        placeholder: lodash.__;
    }
    type LodashPartial1x1<T1, T2, R> = (plc1: [lodash.__, T2]) => lodash.Function1<T1, R>;
    interface LodashPartial1x2<T2> {
        <T1, R>(func: lodash.Function2<T1, T2, R>): lodash.Function1<T1, R>;
        <T1, T3, R>(func: lodash.Function3<T1, T2, T3, R>): lodash.Function2<T1, T3, R>;
        <T1, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>): lodash.Function3<T1, T3, T4, R>;
    }
    interface LodashPartial2x1<T1, T2, T3, R> {
        (plc1: [lodash.__, T2]): lodash.Function2<T1, T3, R>;
        (plc1: [lodash.__, lodash.__, T3]): lodash.Function2<T1, T2, R>;
        (arg1: [T1, lodash.__, T3]): lodash.Function1<T2, R>;
        (plc1: [lodash.__, T2, T3]): lodash.Function1<T1, R>;
    }
    interface LodashPartial3x2<T3> {
        <T1, T2, R>(func: lodash.Function3<T1, T2, T3, R>): lodash.Function2<T1, T2, R>;
        <T1, T2, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>): lodash.Function3<T1, T2, T4, R>;
    }
    interface LodashPartial4x2<T1, T3> {
        <T2, R>(func: lodash.Function3<T1, T2, T3, R>): lodash.Function1<T2, R>;
        <T2, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>): lodash.Function2<T2, T4, R>;
    }
    interface LodashPartial5x2<T2, T3> {
        <T1, R>(func: lodash.Function3<T1, T2, T3, R>): lodash.Function1<T1, R>;
        <T1, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>): lodash.Function2<T1, T4, R>;
    }
    interface LodashPartial6x1<T1, T2, T3, T4, R> {
        (plc1: [lodash.__, T2]): lodash.Function3<T1, T3, T4, R>;
        (plc1: [lodash.__, lodash.__, T3]): lodash.Function3<T1, T2, T4, R>;
        (arg1: [T1, lodash.__, T3]): lodash.Function2<T2, T4, R>;
        (plc1: [lodash.__, T2, T3]): lodash.Function2<T1, T4, R>;
        (arg1: [T1, T2, T3]): lodash.Function1<T4, R>;
        (plc1: [lodash.__, lodash.__, lodash.__, T4]): lodash.Function3<T1, T2, T3, R>;
        (arg1: [T1, lodash.__, lodash.__, T4]): lodash.Function2<T2, T3, R>;
        (plc1: [lodash.__, T2, lodash.__, T4]): lodash.Function2<T1, T3, R>;
        (arg1: [T1, T2, lodash.__, T4]): lodash.Function1<T3, R>;
        (plc1: [lodash.__, lodash.__, T3, T4]): lodash.Function2<T1, T2, R>;
        (arg1: [T1, lodash.__, T3, T4]): lodash.Function1<T2, R>;
        (plc1: [lodash.__, T2, T3, T4]): lodash.Function1<T1, R>;
    }
    interface LodashPartial10x2<T1, T2, T3> {
        <T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>): lodash.Function1<T4, R>;
        <TS extends any[], R>(func: (t1: T1, t2: T2, t3: T3, ...ts: TS) => R): (...ts: TS) => R;
    }
    type LodashPartial11x2<T4> = <T1, T2, T3, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function3<T1, T2, T3, R>;
    type LodashPartial12x2<T1, T4> = <T2, T3, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function2<T2, T3, R>;
    type LodashPartial13x2<T2, T4> = <T1, T3, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function2<T1, T3, R>;
    type LodashPartial14x2<T1, T2, T4> = <T3, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function1<T3, R>;
    type LodashPartial15x2<T3, T4> = <T1, T2, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function2<T1, T2, R>;
    type LodashPartial16x2<T1, T3, T4> = <T2, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function1<T2, R>;
    type LodashPartial17x2<T2, T3, T4> = <T1, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function1<T1, R>;
    type LodashPartial18x1<TS extends any[], T1, R> = (arg1: [T1]) => (...ts: TS) => R;
    type LodashPartial18x2<T1> = <TS extends any[], R>(func: (t1: T1, ...ts: TS) => R) => (...ts: TS) => R;
    type LodashPartial19x1<TS extends any[], T1, T2, R> = (t1: [T1, T2]) => (...ts: TS) => R;
    type LodashPartial19x2<T1, T2> = <TS extends any[], R>(func: (t1: T1, t2: T2, ...ts: TS) => R) => (...ts: TS) => R;
    type LodashPartial20x1<TS extends any[], T1, T2, T3, R> = (t1: [T1, T2, T3]) => (...ts: TS) => R;
    type LodashPartial21x1<TS extends any[], T1, T2, T3, T4, R> = (t1: [T1, T2, T3, T4]) => (...ts: TS) => R;
    type LodashPartial21x2<T1, T2, T3, T4> = <TS extends any[], R>(func: (t1: T1, t2: T2, t3: T3, t4: T4, ...ts: TS) => R) => (...ts: TS) => R;
    interface LodashPartialRight {
        <T1, R>(func: lodash.Function1<T1, R>): LodashPartialRight1x1<T1, R>;
        <T1>(func: lodash.__, arg1: [T1]): LodashPartialRight1x2<T1>;
        <T1, R>(func: lodash.Function1<T1, R>, arg1: [T1]): lodash.Function0<R>;
        <T1, T2, R>(func: lodash.Function2<T1, T2, R>): LodashPartialRight2x1<T1, T2, R>;
        <T1>(func: lodash.__, arg1: [T1, lodash.__]): LodashPartialRight2x2<T1>;
        <T1, T2, R>(func: lodash.Function2<T1, T2, R>, arg1: [T1, lodash.__]): lodash.Function1<T2, R>;
        <T2>(func: lodash.__, arg2: [T2]): LodashPartialRight3x2<T2>;
        <T1, T2, R>(func: lodash.Function2<T1, T2, R>, arg2: [T2]): lodash.Function1<T1, R>;
        <T1, T2>(func: lodash.__, arg1: [T1, T2]): LodashPartialRight4x2<T1, T2>;
        <T1, T2, R>(func: lodash.Function2<T1, T2, R>, arg1: [T1, T2]): lodash.Function0<R>;
        <T1, T2, T3, R>(func: lodash.Function3<T1, T2, T3, R>): LodashPartialRight5x1<T1, T2, T3, R>;
        <T1>(func: lodash.__, arg1: [T1, lodash.__, lodash.__]): LodashPartialRight5x2<T1>;
        <T1, T2, T3, R>(func: lodash.Function3<T1, T2, T3, R>, arg1: [T1, lodash.__, lodash.__]): lodash.Function2<T2, T3, R>;
        <T2>(func: lodash.__, arg2: [T2, lodash.__]): LodashPartialRight6x2<T2>;
        <T1, T2, T3, R>(func: lodash.Function3<T1, T2, T3, R>, arg2: [T2, lodash.__]): lodash.Function2<T1, T3, R>;
        <T1, T2>(func: lodash.__, arg1: [T1, T2, lodash.__]): LodashPartialRight7x2<T1, T2>;
        <T1, T2, T3, R>(func: lodash.Function3<T1, T2, T3, R>, arg1: [T1, T2, lodash.__]): lodash.Function1<T3, R>;
        <T3>(func: lodash.__, arg3: [T3]): LodashPartialRight8x2<T3>;
        <T1, T2, T3, R>(func: lodash.Function3<T1, T2, T3, R>, arg3: [T3]): lodash.Function2<T1, T2, R>;
        <T1, T3>(func: lodash.__, arg1: [T1, lodash.__, T3]): LodashPartialRight9x2<T1, T3>;
        <T1, T2, T3, R>(func: lodash.Function3<T1, T2, T3, R>, arg1: [T1, lodash.__, T3]): lodash.Function1<T2, R>;
        <T2, T3>(func: lodash.__, arg2: [T2, T3]): LodashPartialRight10x2<T2, T3>;
        <T1, T2, T3, R>(func: lodash.Function3<T1, T2, T3, R>, arg2: [T2, T3]): lodash.Function1<T1, R>;
        <T1, T2, T3>(func: lodash.__, arg1: [T1, T2, T3]): LodashPartialRight11x2<T1, T2, T3>;
        <T1, T2, T3, R>(func: lodash.Function3<T1, T2, T3, R>, arg1: [T1, T2, T3]): lodash.Function0<R>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>): LodashPartialRight12x1<T1, T2, T3, T4, R>;
        <T1>(func: lodash.__, arg1: [T1, lodash.__, lodash.__, lodash.__]): LodashPartialRight12x2<T1>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg1: [T1, lodash.__, lodash.__, lodash.__]): lodash.Function3<T2, T3, T4, R>;
        <T2>(func: lodash.__, arg2: [T2, lodash.__, lodash.__]): LodashPartialRight13x2<T2>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg2: [T2, lodash.__, lodash.__]): lodash.Function3<T1, T3, T4, R>;
        <T1, T2>(func: lodash.__, arg1: [T1, T2, lodash.__, lodash.__]): LodashPartialRight14x2<T1, T2>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg1: [T1, T2, lodash.__, lodash.__]): lodash.Function2<T3, T4, R>;
        <T3>(func: lodash.__, arg3: [T3, lodash.__]): LodashPartialRight15x2<T3>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg3: [T3, lodash.__]): lodash.Function3<T1, T2, T4, R>;
        <T1, T3>(func: lodash.__, arg1: [T1, lodash.__, T3, lodash.__]): LodashPartialRight16x2<T1, T3>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg1: [T1, lodash.__, T3, lodash.__]): lodash.Function2<T2, T4, R>;
        <T2, T3>(func: lodash.__, arg2: [T2, T3, lodash.__]): LodashPartialRight17x2<T2, T3>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg2: [T2, T3, lodash.__]): lodash.Function2<T1, T4, R>;
        <T1, T2, T3>(func: lodash.__, arg1: [T1, T2, T3, lodash.__]): LodashPartialRight18x2<T1, T2, T3>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg1: [T1, T2, T3, lodash.__]): lodash.Function1<T4, R>;
        <T4>(func: lodash.__, arg4: [T4]): LodashPartialRight19x2<T4>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg4: [T4]): lodash.Function3<T1, T2, T3, R>;
        <T1, T4>(func: lodash.__, arg1: [T1, lodash.__, lodash.__, T4]): LodashPartialRight20x2<T1, T4>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg1: [T1, lodash.__, lodash.__, T4]): lodash.Function2<T2, T3, R>;
        <T2, T4>(func: lodash.__, arg2: [T2, lodash.__, T4]): LodashPartialRight21x2<T2, T4>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg2: [T2, lodash.__, T4]): lodash.Function2<T1, T3, R>;
        <T1, T2, T4>(func: lodash.__, arg1: [T1, T2, lodash.__, T4]): LodashPartialRight22x2<T1, T2, T4>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg1: [T1, T2, lodash.__, T4]): lodash.Function1<T3, R>;
        <T3, T4>(func: lodash.__, arg3: [T3, T4]): LodashPartialRight23x2<T3, T4>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg3: [T3, T4]): lodash.Function2<T1, T2, R>;
        <T1, T3, T4>(func: lodash.__, arg1: [T1, lodash.__, T3, T4]): LodashPartialRight24x2<T1, T3, T4>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg1: [T1, lodash.__, T3, T4]): lodash.Function1<T2, R>;
        <T2, T3, T4>(func: lodash.__, arg2: [T2, T3, T4]): LodashPartialRight25x2<T2, T3, T4>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg2: [T2, T3, T4]): lodash.Function1<T1, R>;
        <T1, T2, T3, T4>(func: lodash.__, arg1: [T1, T2, T3, T4]): LodashPartialRight26x2<T1, T2, T3, T4>;
        <T1, T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>, arg1: [T1, T2, T3, T4]): lodash.Function0<R>;
        (func: (...args: any[]) => any): LodashPartialRight27x1;
        (func: lodash.__, args: ReadonlyArray<any>): LodashPartialRight27x2;
        (func: (...args: any[]) => any, args: ReadonlyArray<any>): (...args: any[]) => any;
        placeholder: lodash.__;
    }
    type LodashPartialRight1x1<T1, R> = (arg1: [T1]) => lodash.Function0<R>;
    type LodashPartialRight1x2<T1> = <R>(func: lodash.Function1<T1, R>) => lodash.Function0<R>;
    interface LodashPartialRight2x1<T1, T2, R> {
        (arg1: [T1, lodash.__]): lodash.Function1<T2, R>;
        (arg2: [T2]): lodash.Function1<T1, R>;
        (arg1: [T1, T2]): lodash.Function0<R>;
    }
    type LodashPartialRight2x2<T1> = <T2, R>(func: lodash.Function2<T1, T2, R>) => lodash.Function1<T2, R>;
    type LodashPartialRight3x2<T2> = <T1, R>(func: lodash.Function2<T1, T2, R>) => lodash.Function1<T1, R>;
    type LodashPartialRight4x2<T1, T2> = <R>(func: lodash.Function2<T1, T2, R>) => lodash.Function0<R>;
    interface LodashPartialRight5x1<T1, T2, T3, R> {
        (arg1: [T1, lodash.__, lodash.__]): lodash.Function2<T2, T3, R>;
        (arg2: [T2, lodash.__]): lodash.Function2<T1, T3, R>;
        (arg1: [T1, T2, lodash.__]): lodash.Function1<T3, R>;
        (arg3: [T3]): lodash.Function2<T1, T2, R>;
        (arg1: [T1, lodash.__, T3]): lodash.Function1<T2, R>;
        (arg2: [T2, T3]): lodash.Function1<T1, R>;
        (arg1: [T1, T2, T3]): lodash.Function0<R>;
    }
    type LodashPartialRight5x2<T1> = <T2, T3, R>(func: lodash.Function3<T1, T2, T3, R>) => lodash.Function2<T2, T3, R>;
    type LodashPartialRight6x2<T2> = <T1, T3, R>(func: lodash.Function3<T1, T2, T3, R>) => lodash.Function2<T1, T3, R>;
    type LodashPartialRight7x2<T1, T2> = <T3, R>(func: lodash.Function3<T1, T2, T3, R>) => lodash.Function1<T3, R>;
    type LodashPartialRight8x2<T3> = <T1, T2, R>(func: lodash.Function3<T1, T2, T3, R>) => lodash.Function2<T1, T2, R>;
    type LodashPartialRight9x2<T1, T3> = <T2, R>(func: lodash.Function3<T1, T2, T3, R>) => lodash.Function1<T2, R>;
    type LodashPartialRight10x2<T2, T3> = <T1, R>(func: lodash.Function3<T1, T2, T3, R>) => lodash.Function1<T1, R>;
    type LodashPartialRight11x2<T1, T2, T3> = <R>(func: lodash.Function3<T1, T2, T3, R>) => lodash.Function0<R>;
    interface LodashPartialRight12x1<T1, T2, T3, T4, R> {
        (arg1: [T1, lodash.__, lodash.__, lodash.__]): lodash.Function3<T2, T3, T4, R>;
        (arg2: [T2, lodash.__, lodash.__]): lodash.Function3<T1, T3, T4, R>;
        (arg1: [T1, T2, lodash.__, lodash.__]): lodash.Function2<T3, T4, R>;
        (arg3: [T3, lodash.__]): lodash.Function3<T1, T2, T4, R>;
        (arg1: [T1, lodash.__, T3, lodash.__]): lodash.Function2<T2, T4, R>;
        (arg2: [T2, T3, lodash.__]): lodash.Function2<T1, T4, R>;
        (arg1: [T1, T2, T3, lodash.__]): lodash.Function1<T4, R>;
        (arg4: [T4]): lodash.Function3<T1, T2, T3, R>;
        (arg1: [T1, lodash.__, lodash.__, T4]): lodash.Function2<T2, T3, R>;
        (arg2: [T2, lodash.__, T4]): lodash.Function2<T1, T3, R>;
        (arg1: [T1, T2, lodash.__, T4]): lodash.Function1<T3, R>;
        (arg3: [T3, T4]): lodash.Function2<T1, T2, R>;
        (arg1: [T1, lodash.__, T3, T4]): lodash.Function1<T2, R>;
        (arg2: [T2, T3, T4]): lodash.Function1<T1, R>;
        (arg1: [T1, T2, T3, T4]): lodash.Function0<R>;
    }
    type LodashPartialRight12x2<T1> = <T2, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function3<T2, T3, T4, R>;
    type LodashPartialRight13x2<T2> = <T1, T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function3<T1, T3, T4, R>;
    type LodashPartialRight14x2<T1, T2> = <T3, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function2<T3, T4, R>;
    type LodashPartialRight15x2<T3> = <T1, T2, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function3<T1, T2, T4, R>;
    type LodashPartialRight16x2<T1, T3> = <T2, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function2<T2, T4, R>;
    type LodashPartialRight17x2<T2, T3> = <T1, T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function2<T1, T4, R>;
    type LodashPartialRight18x2<T1, T2, T3> = <T4, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function1<T4, R>;
    type LodashPartialRight19x2<T4> = <T1, T2, T3, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function3<T1, T2, T3, R>;
    type LodashPartialRight20x2<T1, T4> = <T2, T3, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function2<T2, T3, R>;
    type LodashPartialRight21x2<T2, T4> = <T1, T3, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function2<T1, T3, R>;
    type LodashPartialRight22x2<T1, T2, T4> = <T3, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function1<T3, R>;
    type LodashPartialRight23x2<T3, T4> = <T1, T2, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function2<T1, T2, R>;
    type LodashPartialRight24x2<T1, T3, T4> = <T2, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function1<T2, R>;
    type LodashPartialRight25x2<T2, T3, T4> = <T1, R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function1<T1, R>;
    type LodashPartialRight26x2<T1, T2, T3, T4> = <R>(func: lodash.Function4<T1, T2, T3, T4, R>) => lodash.Function0<R>;
    type LodashPartialRight27x1 = (args: ReadonlyArray<any>) => (...args: any[]) => any;
    type LodashPartialRight27x2 = (func: (...args: any[]) => any) => (...args: any[]) => any;
    interface LodashPartition {
        <T, U extends T>(callback: lodash.ValueIteratorTypeGuard<T, U>): LodashPartition1x1<T, U>;
        <T>(callback: lodash.__, collection: lodash.List<T> | null | undefined): LodashPartition1x2<T>;
        <T, U extends T>(callback: lodash.ValueIteratorTypeGuard<T, U>, collection: lodash.List<T> | null | undefined): [U[], Array<Exclude<T, U>>];
        <T>(callback: lodash.ValueIteratee<T>): LodashPartition2x1<T>;
        <T>(callback: lodash.ValueIteratee<T>, collection: lodash.List<T> | null | undefined): [T[], T[]];
        <T extends object>(callback: lodash.__, collection: T | null | undefined): LodashPartition3x2<T>;
        <T extends object>(callback: lodash.ValueIteratee<T[keyof T]>, collection: T | null | undefined): [Array<T[keyof T]>, Array<T[keyof T]>];
    }
    type LodashPartition1x1<T, U> = (collection: lodash.List<T> | null | undefined) => [U[], Array<Exclude<T, U>>];
    interface LodashPartition1x2<T> {
        <U extends T>(callback: lodash.ValueIteratorTypeGuard<T, U>): [U[], Array<Exclude<T, U>>];
        (callback: lodash.ValueIteratee<T>): [T[], T[]];
    }
    type LodashPartition2x1<T> = (collection: lodash.List<T> | object | null | undefined) => [T[], T[]];
    type LodashPartition3x2<T> = (callback: lodash.ValueIteratee<T[keyof T]>) => [Array<T[keyof T]>, Array<T[keyof T]>];
    interface LodashPath {
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey]): LodashPath1x1<TObject, TKey>;
        <TObject extends object>(path: lodash.__, object: TObject): LodashPath1x2<TObject>;
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey], object: TObject): TObject[TKey];
        <TObject extends object>(path: lodash.__, object: TObject | null | undefined): LodashPath2x2<TObject>;
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey], object: TObject | null | undefined): TObject[TKey] | undefined;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): LodashPath3x1<TObject, TKey1, TKey2>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2], object: TObject): TObject[TKey1][TKey2];
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2], object: TObject | null | undefined): TObject[TKey1][TKey2] | undefined;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): LodashPath5x1<TObject, TKey1, TKey2, TKey3>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3], object: TObject): TObject[TKey1][TKey2][TKey3];
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3], object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3] | undefined;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): LodashPath7x1<TObject, TKey1, TKey2, TKey3, TKey4>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4], object: TObject): TObject[TKey1][TKey2][TKey3][TKey4];
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4], object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3][TKey4] | undefined;
        (path: number): LodashPath9x1;
        <T>(path: lodash.__, object: lodash.NumericDictionary<T>): LodashPath9x2<T>;
        <T>(path: number, object: lodash.NumericDictionary<T>): T;
        <T>(path: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashPath10x2<T>;
        <T>(path: number, object: lodash.NumericDictionary<T> | null | undefined): T | undefined;
        (path: lodash.PropertyPath): LodashPath11x1;
        (path: lodash.__, object: null | undefined): LodashPath11x2;
        (path: lodash.PropertyPath, object: null | undefined): undefined;
        (path: lodash.__, object: any): LodashPath12x2;
        (path: lodash.PropertyPath, object: any): any;
    }
    interface LodashPath1x1<TObject, TKey extends keyof TObject> {
        (object: TObject): TObject[TKey];
        (object: TObject | null | undefined): TObject[TKey] | undefined;
    }
    interface LodashPath1x2<TObject> {
        <TKey extends keyof TObject>(path: TKey | [TKey]): TObject[TKey];
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): TObject[TKey1][TKey2];
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): TObject[TKey1][TKey2][TKey3];
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): TObject[TKey1][TKey2][TKey3][TKey4];
    }
    interface LodashPath2x2<TObject> {
        <TKey extends keyof TObject>(path: TKey | [TKey]): TObject[TKey] | undefined;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): TObject[TKey1][TKey2] | undefined;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): TObject[TKey1][TKey2][TKey3] | undefined;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): TObject[TKey1][TKey2][TKey3][TKey4] | undefined;
    }
    interface LodashPath3x1<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]> {
        (object: TObject): TObject[TKey1][TKey2];
        (object: TObject | null | undefined): TObject[TKey1][TKey2] | undefined;
    }
    interface LodashPath5x1<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]> {
        (object: TObject): TObject[TKey1][TKey2][TKey3];
        (object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3] | undefined;
    }
    interface LodashPath7x1<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]> {
        (object: TObject): TObject[TKey1][TKey2][TKey3][TKey4];
        (object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3][TKey4] | undefined;
    }
    interface LodashPath9x1 {
        <T>(object: lodash.NumericDictionary<T>): T;
        <T>(object: lodash.NumericDictionary<T> | null | undefined): T | undefined;
    }
    type LodashPath9x2<T> = (path: number) => T;
    type LodashPath10x2<T> = (path: number) => T | undefined;
    interface LodashPath11x1 {
        (object: null | undefined): undefined;
        (object: any): any;
    }
    type LodashPath11x2 = (path: lodash.PropertyPath) => undefined;
    type LodashPath12x2 = (path: lodash.PropertyPath) => any;
    interface LodashPathOr {
        <TDefault>(defaultValue: TDefault): LodashPathOr1x1<TDefault>;
        <TObject extends object, TKey extends keyof TObject>(defaultValue: lodash.__, path: TKey | [TKey]): LodashPathOr1x2<TObject, TKey>;
        <TObject extends object, TKey extends keyof TObject, TDefault>(defaultValue: TDefault, path: TKey | [TKey]): LodashPathOr1x3<TObject, TKey, TDefault>;
        <TObject extends object>(defaultValue: lodash.__, path: lodash.__, object: TObject | null | undefined): LodashPathOr1x4<TObject>;
        <TObject extends object, TDefault>(defaultValue: TDefault, path: lodash.__, object: TObject | null | undefined): LodashPathOr1x5<TObject, TDefault>;
        <TObject extends object, TKey extends keyof TObject>(defaultValue: lodash.__, path: TKey | [TKey], object: TObject | null | undefined): LodashPathOr1x6<TObject, TKey>;
        <TObject extends object, TKey extends keyof TObject, TDefault>(defaultValue: TDefault, path: TKey | [TKey], object: TObject | null | undefined): Exclude<TObject[TKey], undefined> | TDefault;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(defaultValue: lodash.__, path: [TKey1, TKey2]): LodashPathOr2x2<TObject, TKey1, TKey2>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2]): LodashPathOr2x3<TObject, TKey1, TKey2, TDefault>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(defaultValue: lodash.__, path: [TKey1, TKey2], object: TObject | null | undefined): LodashPathOr2x6<TObject, TKey1, TKey2>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2], object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(defaultValue: lodash.__, path: [TKey1, TKey2, TKey3]): LodashPathOr3x2<TObject, TKey1, TKey2, TKey3>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2, TKey3]): LodashPathOr3x3<TObject, TKey1, TKey2, TKey3, TDefault>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(defaultValue: lodash.__, path: [TKey1, TKey2, TKey3], object: TObject | null | undefined): LodashPathOr3x6<TObject, TKey1, TKey2, TKey3>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2, TKey3], object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(defaultValue: lodash.__, path: [TKey1, TKey2, TKey3, TKey4]): LodashPathOr4x2<TObject, TKey1, TKey2, TKey3, TKey4>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2, TKey3, TKey4]): LodashPathOr4x3<TObject, TKey1, TKey2, TKey3, TKey4, TDefault>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(defaultValue: lodash.__, path: [TKey1, TKey2, TKey3, TKey4], object: TObject | null | undefined): LodashPathOr4x6<TObject, TKey1, TKey2, TKey3, TKey4>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2, TKey3, TKey4], object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
        (defaultValue: lodash.__, path: number): LodashPathOr5x2;
        <TDefault>(defaultValue: TDefault, path: number): LodashPathOr5x3<TDefault>;
        <T>(defaultValue: lodash.__, path: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashPathOr5x4<T>;
        <T, TDefault>(defaultValue: TDefault, path: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashPathOr5x5<T, TDefault>;
        <T>(defaultValue: lodash.__, path: number, object: lodash.NumericDictionary<T> | null | undefined): LodashPathOr5x6<T>;
        <T, TDefault>(defaultValue: TDefault, path: number, object: lodash.NumericDictionary<T> | null | undefined): T | TDefault;
        (defaultValue: lodash.__, path: lodash.PropertyPath): LodashPathOr6x2;
        <TDefault>(defaultValue: TDefault, path: lodash.PropertyPath): LodashPathOr6x3<TDefault>;
        (defaultValue: lodash.__, path: lodash.__, object: null | undefined): LodashPathOr6x4;
        <TDefault>(defaultValue: TDefault, path: lodash.__, object: null | undefined): LodashPathOr6x5<TDefault>;
        (defaultValue: lodash.__, path: lodash.PropertyPath, object: null | undefined): LodashPathOr6x6;
        <TDefault>(defaultValue: TDefault, path: lodash.PropertyPath, object: null | undefined): TDefault;
        (defaultValue: any): LodashPathOr7x1;
        (defaultValue: any, path: lodash.PropertyPath): LodashPathOr7x3;
        (defaultValue: lodash.__, path: lodash.__, object: any): LodashPathOr7x4;
        (defaultValue: any, path: lodash.__, object: any): LodashPathOr7x5;
        (defaultValue: lodash.__, path: lodash.PropertyPath, object: any): LodashPathOr7x6;
        (defaultValue: any, path: lodash.PropertyPath, object: any): any;
    }
    interface LodashPathOr1x1<TDefault> {
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey]): LodashPathOr1x3<TObject, TKey, TDefault>;
        <TObject extends object>(path: lodash.__, object: TObject | null | undefined): LodashPathOr1x5<TObject, TDefault>;
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey], object: TObject | null | undefined): Exclude<TObject[TKey], undefined> | TDefault;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): LodashPathOr2x3<TObject, TKey1, TKey2, TDefault>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2], object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): LodashPathOr3x3<TObject, TKey1, TKey2, TKey3, TDefault>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3], object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): LodashPathOr4x3<TObject, TKey1, TKey2, TKey3, TKey4, TDefault>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4], object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
        (path: number): LodashPathOr5x3<TDefault>;
        <T>(path: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashPathOr5x5<T, TDefault>;
        <T>(path: number, object: lodash.NumericDictionary<T> | null | undefined): T | TDefault;
        (path: lodash.PropertyPath): LodashPathOr6x3<TDefault>;
        (path: lodash.__, object: null | undefined): LodashPathOr6x5<TDefault>;
        (path: lodash.PropertyPath, object: null | undefined): TDefault;
    }
    interface LodashPathOr1x2<TObject, TKey extends keyof TObject> {
        <TDefault>(defaultValue: TDefault): LodashPathOr1x3<TObject, TKey, TDefault>;
        (defaultValue: lodash.__, object: TObject | null | undefined): LodashPathOr1x6<TObject, TKey>;
        <TDefault>(defaultValue: TDefault, object: TObject | null | undefined): Exclude<TObject[TKey], undefined> | TDefault;
    }
    type LodashPathOr1x3<TObject, TKey extends keyof TObject, TDefault> = (object: TObject | null | undefined) => Exclude<TObject[TKey], undefined> | TDefault;
    interface LodashPathOr1x4<TObject> {
        <TDefault>(defaultValue: TDefault): LodashPathOr1x5<TObject, TDefault>;
        <TKey extends keyof TObject>(defaultValue: lodash.__, path: TKey | [TKey]): LodashPathOr1x6<TObject, TKey>;
        <TKey extends keyof TObject, TDefault>(defaultValue: TDefault, path: TKey | [TKey]): Exclude<TObject[TKey], undefined> | TDefault;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(defaultValue: lodash.__, path: [TKey1, TKey2]): LodashPathOr2x6<TObject, TKey1, TKey2>;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2]): Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(defaultValue: lodash.__, path: [TKey1, TKey2, TKey3]): LodashPathOr3x6<TObject, TKey1, TKey2, TKey3>;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2, TKey3]): Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(defaultValue: lodash.__, path: [TKey1, TKey2, TKey3, TKey4]): LodashPathOr4x6<TObject, TKey1, TKey2, TKey3, TKey4>;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2, TKey3, TKey4]): Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
    }
    interface LodashPathOr1x5<TObject, TDefault> {
        <TKey extends keyof TObject>(path: TKey | [TKey]): Exclude<TObject[TKey], undefined> | TDefault;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
    }
    type LodashPathOr1x6<TObject, TKey extends keyof TObject> = <TDefault>(defaultValue: TDefault) => Exclude<TObject[TKey], undefined> | TDefault;
    interface LodashPathOr2x2<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]> {
        <TDefault>(defaultValue: TDefault): LodashPathOr2x3<TObject, TKey1, TKey2, TDefault>;
        (defaultValue: lodash.__, object: TObject | null | undefined): LodashPathOr2x6<TObject, TKey1, TKey2>;
        <TDefault>(defaultValue: TDefault, object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
    }
    type LodashPathOr2x3<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TDefault> = (object: TObject | null | undefined) => Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
    type LodashPathOr2x6<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]> = <TDefault>(defaultValue: TDefault) => Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
    interface LodashPathOr3x2<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]> {
        <TDefault>(defaultValue: TDefault): LodashPathOr3x3<TObject, TKey1, TKey2, TKey3, TDefault>;
        (defaultValue: lodash.__, object: TObject | null | undefined): LodashPathOr3x6<TObject, TKey1, TKey2, TKey3>;
        <TDefault>(defaultValue: TDefault, object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
    }
    type LodashPathOr3x3<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TDefault> = (object: TObject | null | undefined) => Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
    type LodashPathOr3x6<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]> = <TDefault>(defaultValue: TDefault) => Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
    interface LodashPathOr4x2<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]> {
        <TDefault>(defaultValue: TDefault): LodashPathOr4x3<TObject, TKey1, TKey2, TKey3, TKey4, TDefault>;
        (defaultValue: lodash.__, object: TObject | null | undefined): LodashPathOr4x6<TObject, TKey1, TKey2, TKey3, TKey4>;
        <TDefault>(defaultValue: TDefault, object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
    }
    type LodashPathOr4x3<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3], TDefault> = (object: TObject | null | undefined) => Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
    type LodashPathOr4x6<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]> = <TDefault>(defaultValue: TDefault) => Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
    interface LodashPathOr5x2 {
        <TDefault>(defaultValue: TDefault): LodashPathOr5x3<TDefault>;
        <T>(defaultValue: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashPathOr5x6<T>;
        <T, TDefault>(defaultValue: TDefault, object: lodash.NumericDictionary<T> | null | undefined): T | TDefault;
    }
    type LodashPathOr5x3<TDefault> = <T>(object: lodash.NumericDictionary<T> | null | undefined) => T | TDefault;
    interface LodashPathOr5x4<T> {
        <TDefault>(defaultValue: TDefault): LodashPathOr5x5<T, TDefault>;
        (defaultValue: lodash.__, path: number): LodashPathOr5x6<T>;
        <TDefault>(defaultValue: TDefault, path: number): T | TDefault;
    }
    type LodashPathOr5x5<T, TDefault> = (path: number) => T | TDefault;
    type LodashPathOr5x6<T> = <TDefault>(defaultValue: TDefault) => T | TDefault;
    interface LodashPathOr6x2 {
        <TDefault>(defaultValue: TDefault): LodashPathOr6x3<TDefault>;
        (defaultValue: lodash.__, object: null | undefined): LodashPathOr6x6;
        <TDefault>(defaultValue: TDefault, object: null | undefined): TDefault;
        (defaultValue: any): LodashPathOr7x3;
        (defaultValue: lodash.__, object: any): LodashPathOr7x6;
        (defaultValue: any, object: any): any;
    }
    type LodashPathOr6x3<TDefault> = (object: null | undefined) => TDefault;
    interface LodashPathOr6x4 {
        <TDefault>(defaultValue: TDefault): LodashPathOr6x5<TDefault>;
        (defaultValue: lodash.__, path: lodash.PropertyPath): LodashPathOr6x6;
        <TDefault>(defaultValue: TDefault, path: lodash.PropertyPath): TDefault;
    }
    type LodashPathOr6x5<TDefault> = (path: lodash.PropertyPath) => TDefault;
    type LodashPathOr6x6 = <TDefault>(defaultValue: TDefault) => TDefault;
    interface LodashPathOr7x1 {
        (path: lodash.PropertyPath): LodashPathOr7x3;
        (path: lodash.__, object: any): LodashPathOr7x5;
        (path: lodash.PropertyPath, object: any): any;
    }
    type LodashPathOr7x3 = (object: any) => any;
    interface LodashPathOr7x4 {
        (defaultValue: any): LodashPathOr7x5;
        (defaultValue: lodash.__, path: lodash.PropertyPath): LodashPathOr7x6;
        (defaultValue: any, path: lodash.PropertyPath): any;
    }
    type LodashPathOr7x5 = (path: lodash.PropertyPath) => any;
    type LodashPathOr7x6 = (defaultValue: any) => any;
    interface LodashPick {
        <T extends object, U extends keyof T>(props: lodash.Many<U>): LodashPick1x1<T, U>;
        <T extends object>(props: lodash.__, object: T): LodashPick1x2<T>;
        <T extends object, U extends keyof T>(props: lodash.Many<U>, object: T): Pick<T, U>;
        (props: lodash.PropertyPath): LodashPick2x1;
        <T>(props: lodash.__, object: T | null | undefined): LodashPick2x2<T>;
        <T>(props: lodash.PropertyPath, object: T | null | undefined): lodash.PartialObject<T>;
    }
    type LodashPick1x1<T, U extends keyof T> = (object: T) => Pick<T, U>;
    type LodashPick1x2<T> = <U extends keyof T>(props: lodash.Many<U>) => Pick<T, U>;
    type LodashPick2x1 = <T>(object: T | null | undefined) => lodash.PartialObject<T>;
    type LodashPick2x2<T> = (props: lodash.PropertyPath) => lodash.PartialObject<T>;
    interface LodashPickBy {
        <T, S extends T>(predicate: lodash.ValueKeyIterateeTypeGuard<T, S>): LodashPickBy1x1<T, S>;
        <T>(predicate: lodash.__, object: lodash.Dictionary<T> | null | undefined): LodashPickBy1x2<T>;
        <T, S extends T>(predicate: lodash.ValueKeyIterateeTypeGuard<T, S>, object: lodash.Dictionary<T> | null | undefined): lodash.Dictionary<S>;
        <T>(predicate: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashPickBy2x2<T>;
        <T, S extends T>(predicate: lodash.ValueKeyIterateeTypeGuard<T, S>, object: lodash.NumericDictionary<T> | null | undefined): lodash.NumericDictionary<S>;
        <T>(predicate: lodash.ValueKeyIteratee<T>): LodashPickBy3x1<T>;
        <T>(predicate: lodash.ValueKeyIteratee<T>, object: lodash.Dictionary<T> | null | undefined): lodash.Dictionary<T>;
        <T>(predicate: lodash.ValueKeyIteratee<T>, object: lodash.NumericDictionary<T> | null | undefined): lodash.NumericDictionary<T>;
        <T extends object>(predicate: lodash.__, object: T | null | undefined): LodashPickBy5x2<T>;
        <T extends object>(predicate: lodash.ValueKeyIteratee<T[keyof T]>, object: T | null | undefined): lodash.PartialObject<T>;
    }
    interface LodashPickBy1x1<T, S> {
        (object: lodash.Dictionary<T> | null | undefined): lodash.Dictionary<S>;
        (object: lodash.NumericDictionary<T> | null | undefined): lodash.NumericDictionary<S>;
    }
    interface LodashPickBy1x2<T> {
        <S extends T>(predicate: lodash.ValueKeyIterateeTypeGuard<T, S>): lodash.Dictionary<S>;
        (predicate: lodash.ValueKeyIteratee<T>): lodash.Dictionary<T>;
    }
    interface LodashPickBy2x2<T> {
        <S extends T>(predicate: lodash.ValueKeyIterateeTypeGuard<T, S>): lodash.NumericDictionary<S>;
        (predicate: lodash.ValueKeyIteratee<T>): lodash.NumericDictionary<T>;
    }
    interface LodashPickBy3x1<T> {
        (object: lodash.Dictionary<T> | null | undefined): lodash.Dictionary<T>;
        (object: lodash.NumericDictionary<T> | null | undefined): lodash.NumericDictionary<T>;
        <T1 extends object>(object: T1 | null | undefined): lodash.PartialObject<T1>;
    }
    type LodashPickBy5x2<T> = (predicate: lodash.ValueKeyIteratee<T[keyof T]>) => lodash.PartialObject<T>;
    interface LodashProp {
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey]): LodashProp1x1<TObject, TKey>;
        <TObject extends object>(path: lodash.__, object: TObject): LodashProp1x2<TObject>;
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey], object: TObject): TObject[TKey];
        <TObject extends object>(path: lodash.__, object: TObject | null | undefined): LodashProp2x2<TObject>;
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey], object: TObject | null | undefined): TObject[TKey] | undefined;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): LodashProp3x1<TObject, TKey1, TKey2>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2], object: TObject): TObject[TKey1][TKey2];
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2], object: TObject | null | undefined): TObject[TKey1][TKey2] | undefined;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): LodashProp5x1<TObject, TKey1, TKey2, TKey3>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3], object: TObject): TObject[TKey1][TKey2][TKey3];
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3], object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3] | undefined;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): LodashProp7x1<TObject, TKey1, TKey2, TKey3, TKey4>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4], object: TObject): TObject[TKey1][TKey2][TKey3][TKey4];
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4], object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3][TKey4] | undefined;
        (path: number): LodashProp9x1;
        <T>(path: lodash.__, object: lodash.NumericDictionary<T>): LodashProp9x2<T>;
        <T>(path: number, object: lodash.NumericDictionary<T>): T;
        <T>(path: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashProp10x2<T>;
        <T>(path: number, object: lodash.NumericDictionary<T> | null | undefined): T | undefined;
        (path: lodash.PropertyPath): LodashProp11x1;
        (path: lodash.__, object: null | undefined): LodashProp11x2;
        (path: lodash.PropertyPath, object: null | undefined): undefined;
        (path: lodash.__, object: any): LodashProp12x2;
        (path: lodash.PropertyPath, object: any): any;
    }
    interface LodashProp1x1<TObject, TKey extends keyof TObject> {
        (object: TObject): TObject[TKey];
        (object: TObject | null | undefined): TObject[TKey] | undefined;
    }
    interface LodashProp1x2<TObject> {
        <TKey extends keyof TObject>(path: TKey | [TKey]): TObject[TKey];
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): TObject[TKey1][TKey2];
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): TObject[TKey1][TKey2][TKey3];
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): TObject[TKey1][TKey2][TKey3][TKey4];
    }
    interface LodashProp2x2<TObject> {
        <TKey extends keyof TObject>(path: TKey | [TKey]): TObject[TKey] | undefined;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): TObject[TKey1][TKey2] | undefined;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): TObject[TKey1][TKey2][TKey3] | undefined;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): TObject[TKey1][TKey2][TKey3][TKey4] | undefined;
    }
    interface LodashProp3x1<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]> {
        (object: TObject): TObject[TKey1][TKey2];
        (object: TObject | null | undefined): TObject[TKey1][TKey2] | undefined;
    }
    interface LodashProp5x1<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]> {
        (object: TObject): TObject[TKey1][TKey2][TKey3];
        (object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3] | undefined;
    }
    interface LodashProp7x1<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]> {
        (object: TObject): TObject[TKey1][TKey2][TKey3][TKey4];
        (object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3][TKey4] | undefined;
    }
    interface LodashProp9x1 {
        <T>(object: lodash.NumericDictionary<T>): T;
        <T>(object: lodash.NumericDictionary<T> | null | undefined): T | undefined;
    }
    type LodashProp9x2<T> = (path: number) => T;
    type LodashProp10x2<T> = (path: number) => T | undefined;
    interface LodashProp11x1 {
        (object: null | undefined): undefined;
        (object: any): any;
    }
    type LodashProp11x2 = (path: lodash.PropertyPath) => undefined;
    type LodashProp12x2 = (path: lodash.PropertyPath) => any;
    interface LodashProperty {
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey]): LodashProperty1x1<TObject, TKey>;
        <TObject extends object>(path: lodash.__, object: TObject): LodashProperty1x2<TObject>;
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey], object: TObject): TObject[TKey];
        <TObject extends object>(path: lodash.__, object: TObject | null | undefined): LodashProperty2x2<TObject>;
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey], object: TObject | null | undefined): TObject[TKey] | undefined;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): LodashProperty3x1<TObject, TKey1, TKey2>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2], object: TObject): TObject[TKey1][TKey2];
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2], object: TObject | null | undefined): TObject[TKey1][TKey2] | undefined;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): LodashProperty5x1<TObject, TKey1, TKey2, TKey3>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3], object: TObject): TObject[TKey1][TKey2][TKey3];
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3], object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3] | undefined;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): LodashProperty7x1<TObject, TKey1, TKey2, TKey3, TKey4>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4], object: TObject): TObject[TKey1][TKey2][TKey3][TKey4];
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4], object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3][TKey4] | undefined;
        (path: number): LodashProperty9x1;
        <T>(path: lodash.__, object: lodash.NumericDictionary<T>): LodashProperty9x2<T>;
        <T>(path: number, object: lodash.NumericDictionary<T>): T;
        <T>(path: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashProperty10x2<T>;
        <T>(path: number, object: lodash.NumericDictionary<T> | null | undefined): T | undefined;
        (path: lodash.PropertyPath): LodashProperty11x1;
        (path: lodash.__, object: null | undefined): LodashProperty11x2;
        (path: lodash.PropertyPath, object: null | undefined): undefined;
        (path: lodash.__, object: any): LodashProperty12x2;
        (path: lodash.PropertyPath, object: any): any;
    }
    interface LodashProperty1x1<TObject, TKey extends keyof TObject> {
        (object: TObject): TObject[TKey];
        (object: TObject | null | undefined): TObject[TKey] | undefined;
    }
    interface LodashProperty1x2<TObject> {
        <TKey extends keyof TObject>(path: TKey | [TKey]): TObject[TKey];
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): TObject[TKey1][TKey2];
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): TObject[TKey1][TKey2][TKey3];
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): TObject[TKey1][TKey2][TKey3][TKey4];
    }
    interface LodashProperty2x2<TObject> {
        <TKey extends keyof TObject>(path: TKey | [TKey]): TObject[TKey] | undefined;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): TObject[TKey1][TKey2] | undefined;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): TObject[TKey1][TKey2][TKey3] | undefined;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): TObject[TKey1][TKey2][TKey3][TKey4] | undefined;
    }
    interface LodashProperty3x1<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]> {
        (object: TObject): TObject[TKey1][TKey2];
        (object: TObject | null | undefined): TObject[TKey1][TKey2] | undefined;
    }
    interface LodashProperty5x1<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]> {
        (object: TObject): TObject[TKey1][TKey2][TKey3];
        (object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3] | undefined;
    }
    interface LodashProperty7x1<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]> {
        (object: TObject): TObject[TKey1][TKey2][TKey3][TKey4];
        (object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3][TKey4] | undefined;
    }
    interface LodashProperty9x1 {
        <T>(object: lodash.NumericDictionary<T>): T;
        <T>(object: lodash.NumericDictionary<T> | null | undefined): T | undefined;
    }
    type LodashProperty9x2<T> = (path: number) => T;
    type LodashProperty10x2<T> = (path: number) => T | undefined;
    interface LodashProperty11x1 {
        (object: null | undefined): undefined;
        (object: any): any;
    }
    type LodashProperty11x2 = (path: lodash.PropertyPath) => undefined;
    type LodashProperty12x2 = (path: lodash.PropertyPath) => any;
    interface LodashPropertyOf {
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey]): LodashPropertyOf1x1<TObject, TKey>;
        <TObject extends object>(path: lodash.__, object: TObject): LodashPropertyOf1x2<TObject>;
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey], object: TObject): TObject[TKey];
        <TObject extends object>(path: lodash.__, object: TObject | null | undefined): LodashPropertyOf2x2<TObject>;
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey], object: TObject | null | undefined): TObject[TKey] | undefined;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): LodashPropertyOf3x1<TObject, TKey1, TKey2>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2], object: TObject): TObject[TKey1][TKey2];
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2], object: TObject | null | undefined): TObject[TKey1][TKey2] | undefined;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): LodashPropertyOf5x1<TObject, TKey1, TKey2, TKey3>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3], object: TObject): TObject[TKey1][TKey2][TKey3];
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3], object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3] | undefined;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): LodashPropertyOf7x1<TObject, TKey1, TKey2, TKey3, TKey4>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4], object: TObject): TObject[TKey1][TKey2][TKey3][TKey4];
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4], object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3][TKey4] | undefined;
        (path: number): LodashPropertyOf9x1;
        <T>(path: lodash.__, object: lodash.NumericDictionary<T>): LodashPropertyOf9x2<T>;
        <T>(path: number, object: lodash.NumericDictionary<T>): T;
        <T>(path: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashPropertyOf10x2<T>;
        <T>(path: number, object: lodash.NumericDictionary<T> | null | undefined): T | undefined;
        (path: lodash.PropertyPath): LodashPropertyOf11x1;
        (path: lodash.__, object: null | undefined): LodashPropertyOf11x2;
        (path: lodash.PropertyPath, object: null | undefined): undefined;
        (path: lodash.__, object: any): LodashPropertyOf12x2;
        (path: lodash.PropertyPath, object: any): any;
    }
    interface LodashPropertyOf1x1<TObject, TKey extends keyof TObject> {
        (object: TObject): TObject[TKey];
        (object: TObject | null | undefined): TObject[TKey] | undefined;
    }
    interface LodashPropertyOf1x2<TObject> {
        <TKey extends keyof TObject>(path: TKey | [TKey]): TObject[TKey];
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): TObject[TKey1][TKey2];
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): TObject[TKey1][TKey2][TKey3];
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): TObject[TKey1][TKey2][TKey3][TKey4];
    }
    interface LodashPropertyOf2x2<TObject> {
        <TKey extends keyof TObject>(path: TKey | [TKey]): TObject[TKey] | undefined;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): TObject[TKey1][TKey2] | undefined;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): TObject[TKey1][TKey2][TKey3] | undefined;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): TObject[TKey1][TKey2][TKey3][TKey4] | undefined;
    }
    interface LodashPropertyOf3x1<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]> {
        (object: TObject): TObject[TKey1][TKey2];
        (object: TObject | null | undefined): TObject[TKey1][TKey2] | undefined;
    }
    interface LodashPropertyOf5x1<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]> {
        (object: TObject): TObject[TKey1][TKey2][TKey3];
        (object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3] | undefined;
    }
    interface LodashPropertyOf7x1<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]> {
        (object: TObject): TObject[TKey1][TKey2][TKey3][TKey4];
        (object: TObject | null | undefined): TObject[TKey1][TKey2][TKey3][TKey4] | undefined;
    }
    interface LodashPropertyOf9x1 {
        <T>(object: lodash.NumericDictionary<T>): T;
        <T>(object: lodash.NumericDictionary<T> | null | undefined): T | undefined;
    }
    type LodashPropertyOf9x2<T> = (path: number) => T;
    type LodashPropertyOf10x2<T> = (path: number) => T | undefined;
    interface LodashPropertyOf11x1 {
        (object: null | undefined): undefined;
        (object: any): any;
    }
    type LodashPropertyOf11x2 = (path: lodash.PropertyPath) => undefined;
    type LodashPropertyOf12x2 = (path: lodash.PropertyPath) => any;
    interface LodashPropOr {
        <TDefault>(defaultValue: TDefault): LodashPropOr1x1<TDefault>;
        <TObject extends object, TKey extends keyof TObject>(defaultValue: lodash.__, path: TKey | [TKey]): LodashPropOr1x2<TObject, TKey>;
        <TObject extends object, TKey extends keyof TObject, TDefault>(defaultValue: TDefault, path: TKey | [TKey]): LodashPropOr1x3<TObject, TKey, TDefault>;
        <TObject extends object>(defaultValue: lodash.__, path: lodash.__, object: TObject | null | undefined): LodashPropOr1x4<TObject>;
        <TObject extends object, TDefault>(defaultValue: TDefault, path: lodash.__, object: TObject | null | undefined): LodashPropOr1x5<TObject, TDefault>;
        <TObject extends object, TKey extends keyof TObject>(defaultValue: lodash.__, path: TKey | [TKey], object: TObject | null | undefined): LodashPropOr1x6<TObject, TKey>;
        <TObject extends object, TKey extends keyof TObject, TDefault>(defaultValue: TDefault, path: TKey | [TKey], object: TObject | null | undefined): Exclude<TObject[TKey], undefined> | TDefault;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(defaultValue: lodash.__, path: [TKey1, TKey2]): LodashPropOr2x2<TObject, TKey1, TKey2>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2]): LodashPropOr2x3<TObject, TKey1, TKey2, TDefault>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(defaultValue: lodash.__, path: [TKey1, TKey2], object: TObject | null | undefined): LodashPropOr2x6<TObject, TKey1, TKey2>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2], object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(defaultValue: lodash.__, path: [TKey1, TKey2, TKey3]): LodashPropOr3x2<TObject, TKey1, TKey2, TKey3>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2, TKey3]): LodashPropOr3x3<TObject, TKey1, TKey2, TKey3, TDefault>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(defaultValue: lodash.__, path: [TKey1, TKey2, TKey3], object: TObject | null | undefined): LodashPropOr3x6<TObject, TKey1, TKey2, TKey3>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2, TKey3], object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(defaultValue: lodash.__, path: [TKey1, TKey2, TKey3, TKey4]): LodashPropOr4x2<TObject, TKey1, TKey2, TKey3, TKey4>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2, TKey3, TKey4]): LodashPropOr4x3<TObject, TKey1, TKey2, TKey3, TKey4, TDefault>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(defaultValue: lodash.__, path: [TKey1, TKey2, TKey3, TKey4], object: TObject | null | undefined): LodashPropOr4x6<TObject, TKey1, TKey2, TKey3, TKey4>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2, TKey3, TKey4], object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
        (defaultValue: lodash.__, path: number): LodashPropOr5x2;
        <TDefault>(defaultValue: TDefault, path: number): LodashPropOr5x3<TDefault>;
        <T>(defaultValue: lodash.__, path: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashPropOr5x4<T>;
        <T, TDefault>(defaultValue: TDefault, path: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashPropOr5x5<T, TDefault>;
        <T>(defaultValue: lodash.__, path: number, object: lodash.NumericDictionary<T> | null | undefined): LodashPropOr5x6<T>;
        <T, TDefault>(defaultValue: TDefault, path: number, object: lodash.NumericDictionary<T> | null | undefined): T | TDefault;
        (defaultValue: lodash.__, path: lodash.PropertyPath): LodashPropOr6x2;
        <TDefault>(defaultValue: TDefault, path: lodash.PropertyPath): LodashPropOr6x3<TDefault>;
        (defaultValue: lodash.__, path: lodash.__, object: null | undefined): LodashPropOr6x4;
        <TDefault>(defaultValue: TDefault, path: lodash.__, object: null | undefined): LodashPropOr6x5<TDefault>;
        (defaultValue: lodash.__, path: lodash.PropertyPath, object: null | undefined): LodashPropOr6x6;
        <TDefault>(defaultValue: TDefault, path: lodash.PropertyPath, object: null | undefined): TDefault;
        (defaultValue: any): LodashPropOr7x1;
        (defaultValue: any, path: lodash.PropertyPath): LodashPropOr7x3;
        (defaultValue: lodash.__, path: lodash.__, object: any): LodashPropOr7x4;
        (defaultValue: any, path: lodash.__, object: any): LodashPropOr7x5;
        (defaultValue: lodash.__, path: lodash.PropertyPath, object: any): LodashPropOr7x6;
        (defaultValue: any, path: lodash.PropertyPath, object: any): any;
    }
    interface LodashPropOr1x1<TDefault> {
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey]): LodashPropOr1x3<TObject, TKey, TDefault>;
        <TObject extends object>(path: lodash.__, object: TObject | null | undefined): LodashPropOr1x5<TObject, TDefault>;
        <TObject extends object, TKey extends keyof TObject>(path: TKey | [TKey], object: TObject | null | undefined): Exclude<TObject[TKey], undefined> | TDefault;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): LodashPropOr2x3<TObject, TKey1, TKey2, TDefault>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2], object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): LodashPropOr3x3<TObject, TKey1, TKey2, TKey3, TDefault>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3], object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): LodashPropOr4x3<TObject, TKey1, TKey2, TKey3, TKey4, TDefault>;
        <TObject extends object, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4], object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
        (path: number): LodashPropOr5x3<TDefault>;
        <T>(path: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashPropOr5x5<T, TDefault>;
        <T>(path: number, object: lodash.NumericDictionary<T> | null | undefined): T | TDefault;
        (path: lodash.PropertyPath): LodashPropOr6x3<TDefault>;
        (path: lodash.__, object: null | undefined): LodashPropOr6x5<TDefault>;
        (path: lodash.PropertyPath, object: null | undefined): TDefault;
    }
    interface LodashPropOr1x2<TObject, TKey extends keyof TObject> {
        <TDefault>(defaultValue: TDefault): LodashPropOr1x3<TObject, TKey, TDefault>;
        (defaultValue: lodash.__, object: TObject | null | undefined): LodashPropOr1x6<TObject, TKey>;
        <TDefault>(defaultValue: TDefault, object: TObject | null | undefined): Exclude<TObject[TKey], undefined> | TDefault;
    }
    type LodashPropOr1x3<TObject, TKey extends keyof TObject, TDefault> = (object: TObject | null | undefined) => Exclude<TObject[TKey], undefined> | TDefault;
    interface LodashPropOr1x4<TObject> {
        <TDefault>(defaultValue: TDefault): LodashPropOr1x5<TObject, TDefault>;
        <TKey extends keyof TObject>(defaultValue: lodash.__, path: TKey | [TKey]): LodashPropOr1x6<TObject, TKey>;
        <TKey extends keyof TObject, TDefault>(defaultValue: TDefault, path: TKey | [TKey]): Exclude<TObject[TKey], undefined> | TDefault;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(defaultValue: lodash.__, path: [TKey1, TKey2]): LodashPropOr2x6<TObject, TKey1, TKey2>;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2]): Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(defaultValue: lodash.__, path: [TKey1, TKey2, TKey3]): LodashPropOr3x6<TObject, TKey1, TKey2, TKey3>;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2, TKey3]): Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(defaultValue: lodash.__, path: [TKey1, TKey2, TKey3, TKey4]): LodashPropOr4x6<TObject, TKey1, TKey2, TKey3, TKey4>;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3], TDefault>(defaultValue: TDefault, path: [TKey1, TKey2, TKey3, TKey4]): Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
    }
    interface LodashPropOr1x5<TObject, TDefault> {
        <TKey extends keyof TObject>(path: TKey | [TKey]): Exclude<TObject[TKey], undefined> | TDefault;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]>(path: [TKey1, TKey2]): Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]>(path: [TKey1, TKey2, TKey3]): Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
        <TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]>(path: [TKey1, TKey2, TKey3, TKey4]): Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
    }
    type LodashPropOr1x6<TObject, TKey extends keyof TObject> = <TDefault>(defaultValue: TDefault) => Exclude<TObject[TKey], undefined> | TDefault;
    interface LodashPropOr2x2<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]> {
        <TDefault>(defaultValue: TDefault): LodashPropOr2x3<TObject, TKey1, TKey2, TDefault>;
        (defaultValue: lodash.__, object: TObject | null | undefined): LodashPropOr2x6<TObject, TKey1, TKey2>;
        <TDefault>(defaultValue: TDefault, object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
    }
    type LodashPropOr2x3<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TDefault> = (object: TObject | null | undefined) => Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
    type LodashPropOr2x6<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1]> = <TDefault>(defaultValue: TDefault) => Exclude<TObject[TKey1][TKey2], undefined> | TDefault;
    interface LodashPropOr3x2<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]> {
        <TDefault>(defaultValue: TDefault): LodashPropOr3x3<TObject, TKey1, TKey2, TKey3, TDefault>;
        (defaultValue: lodash.__, object: TObject | null | undefined): LodashPropOr3x6<TObject, TKey1, TKey2, TKey3>;
        <TDefault>(defaultValue: TDefault, object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
    }
    type LodashPropOr3x3<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TDefault> = (object: TObject | null | undefined) => Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
    type LodashPropOr3x6<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2]> = <TDefault>(defaultValue: TDefault) => Exclude<TObject[TKey1][TKey2][TKey3], undefined> | TDefault;
    interface LodashPropOr4x2<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]> {
        <TDefault>(defaultValue: TDefault): LodashPropOr4x3<TObject, TKey1, TKey2, TKey3, TKey4, TDefault>;
        (defaultValue: lodash.__, object: TObject | null | undefined): LodashPropOr4x6<TObject, TKey1, TKey2, TKey3, TKey4>;
        <TDefault>(defaultValue: TDefault, object: TObject | null | undefined): Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
    }
    type LodashPropOr4x3<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3], TDefault> = (object: TObject | null | undefined) => Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
    type LodashPropOr4x6<TObject, TKey1 extends keyof TObject, TKey2 extends keyof TObject[TKey1], TKey3 extends keyof TObject[TKey1][TKey2], TKey4 extends keyof TObject[TKey1][TKey2][TKey3]> = <TDefault>(defaultValue: TDefault) => Exclude<TObject[TKey1][TKey2][TKey3][TKey4], undefined> | TDefault;
    interface LodashPropOr5x2 {
        <TDefault>(defaultValue: TDefault): LodashPropOr5x3<TDefault>;
        <T>(defaultValue: lodash.__, object: lodash.NumericDictionary<T> | null | undefined): LodashPropOr5x6<T>;
        <T, TDefault>(defaultValue: TDefault, object: lodash.NumericDictionary<T> | null | undefined): T | TDefault;
    }
    type LodashPropOr5x3<TDefault> = <T>(object: lodash.NumericDictionary<T> | null | undefined) => T | TDefault;
    interface LodashPropOr5x4<T> {
        <TDefault>(defaultValue: TDefault): LodashPropOr5x5<T, TDefault>;
        (defaultValue: lodash.__, path: number): LodashPropOr5x6<T>;
        <TDefault>(defaultValue: TDefault, path: number): T | TDefault;
    }
    type LodashPropOr5x5<T, TDefault> = (path: number) => T | TDefault;
    type LodashPropOr5x6<T> = <TDefault>(defaultValue: TDefault) => T | TDefault;
    interface LodashPropOr6x2 {
        <TDefault>(defaultValue: TDefault): LodashPropOr6x3<TDefault>;
        (defaultValue: lodash.__, object: null | undefined): LodashPropOr6x6;
        <TDefault>(defaultValue: TDefault, object: null | undefined): TDefault;
        (defaultValue: any): LodashPropOr7x3;
        (defaultValue: lodash.__, object: any): LodashPropOr7x6;
        (defaultValue: any, object: any): any;
    }
    type LodashPropOr6x3<TDefault> = (object: null | undefined) => TDefault;
    interface LodashPropOr6x4 {
        <TDefault>(defaultValue: TDefault): LodashPropOr6x5<TDefault>;
        (defaultValue: lodash.__, path: lodash.PropertyPath): LodashPropOr6x6;
        <TDefault>(defaultValue: TDefault, path: lodash.PropertyPath): TDefault;
    }
    type LodashPropOr6x5<TDefault> = (path: lodash.PropertyPath) => TDefault;
    type LodashPropOr6x6 = <TDefault>(defaultValue: TDefault) => TDefault;
    interface LodashPropOr7x1 {
        (path: lodash.PropertyPath): LodashPropOr7x3;
        (path: lodash.__, object: any): LodashPropOr7x5;
        (path: lodash.PropertyPath, object: any): any;
    }
    type LodashPropOr7x3 = (object: any) => any;
    interface LodashPropOr7x4 {
        (defaultValue: any): LodashPropOr7x5;
        (defaultValue: lodash.__, path: lodash.PropertyPath): LodashPropOr7x6;
        (defaultValue: any, path: lodash.PropertyPath): any;
    }
    type LodashPropOr7x5 = (path: lodash.PropertyPath) => any;
    type LodashPropOr7x6 = (defaultValue: any) => any;
    interface LodashPull {
        <T>(values: T): LodashPull1x1<T>;
        <T>(values: lodash.__, array: ReadonlyArray<T>): LodashPull1x2<T>;
        <T>(values: T, array: ReadonlyArray<T>): T[];
        <T>(values: lodash.__, array: lodash.List<T>): LodashPull2x2<T>;
        <T>(values: T, array: lodash.List<T>): lodash.List<T>;
    }
    interface LodashPull1x1<T> {
        (array: ReadonlyArray<T>): T[];
        (array: lodash.List<T>): lodash.List<T>;
    }
    type LodashPull1x2<T> = (values: T) => T[];
    type LodashPull2x2<T> = (values: T) => lodash.List<T>;
    interface LodashPullAll {
        <T>(values: lodash.List<T>): LodashPullAll1x1<T>;
        <T>(values: lodash.__, array: ReadonlyArray<T>): LodashPullAll1x2<T>;
        <T>(values: lodash.List<T>, array: ReadonlyArray<T>): T[];
        <T>(values: lodash.__, array: lodash.List<T>): LodashPullAll2x2<T>;
        <T>(values: lodash.List<T>, array: lodash.List<T>): lodash.List<T>;
    }
    interface LodashPullAll1x1<T> {
        (array: ReadonlyArray<T>): T[];
        (array: lodash.List<T>): lodash.List<T>;
    }
    type LodashPullAll1x2<T> = (values: lodash.List<T>) => T[];
    type LodashPullAll2x2<T> = (values: lodash.List<T>) => lodash.List<T>;
    interface LodashPullAllBy {
        <T>(iteratee: lodash.ValueIteratee<T>): LodashPullAllBy1x1<T>;
        <T>(iteratee: lodash.__, values: lodash.List<T>): LodashPullAllBy1x2<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, values: lodash.List<T>): LodashPullAllBy1x3<T>;
        <T>(iteratee: lodash.__, values: lodash.__, array: ReadonlyArray<T>): LodashPullAllBy1x4<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, values: lodash.__, array: ReadonlyArray<T>): LodashPullAllBy1x5<T>;
        <T>(iteratee: lodash.__, values: lodash.List<T>, array: ReadonlyArray<T>): LodashPullAllBy1x6<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, values: lodash.List<T>, array: ReadonlyArray<T>): T[];
        <T>(iteratee: lodash.__, values: lodash.__, array: lodash.List<T>): LodashPullAllBy2x4<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, values: lodash.__, array: lodash.List<T>): LodashPullAllBy2x5<T>;
        <T>(iteratee: lodash.__, values: lodash.List<T>, array: lodash.List<T>): LodashPullAllBy2x6<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, values: lodash.List<T>, array: lodash.List<T>): lodash.List<T>;
        <T1, T2>(iteratee: lodash.ValueIteratee<T1 | T2>): LodashPullAllBy3x1<T1, T2>;
        <T2>(iteratee: lodash.__, values: lodash.List<T2>): LodashPullAllBy3x2<T2>;
        <T1, T2>(iteratee: lodash.ValueIteratee<T1 | T2>, values: lodash.List<T2>): LodashPullAllBy3x3<T1>;
        <T1>(iteratee: lodash.__, values: lodash.__, array: ReadonlyArray<T1>): LodashPullAllBy3x4<T1>;
        <T1, T2>(iteratee: lodash.ValueIteratee<T1 | T2>, values: lodash.__, array: ReadonlyArray<T1>): LodashPullAllBy3x5<T1, T2>;
        <T1, T2>(iteratee: lodash.__, values: lodash.List<T2>, array: ReadonlyArray<T1>): LodashPullAllBy3x6<T1, T2>;
        <T1, T2>(iteratee: lodash.ValueIteratee<T1 | T2>, values: lodash.List<T2>, array: ReadonlyArray<T1>): T1[];
        <T1>(iteratee: lodash.__, values: lodash.__, array: lodash.List<T1>): LodashPullAllBy4x4<T1>;
        <T1, T2>(iteratee: lodash.ValueIteratee<T1 | T2>, values: lodash.__, array: lodash.List<T1>): LodashPullAllBy4x5<T1, T2>;
        <T1, T2>(iteratee: lodash.__, values: lodash.List<T2>, array: lodash.List<T1>): LodashPullAllBy4x6<T1, T2>;
        <T1, T2>(iteratee: lodash.ValueIteratee<T1 | T2>, values: lodash.List<T2>, array: lodash.List<T1>): lodash.List<T1>;
    }
    interface LodashPullAllBy1x1<T> {
        (values: lodash.List<T>): LodashPullAllBy1x3<T>;
        (values: lodash.__, array: ReadonlyArray<T>): LodashPullAllBy1x5<T>;
        (values: lodash.List<T>, array: ReadonlyArray<T>): T[];
        (values: lodash.__, array: lodash.List<T>): LodashPullAllBy2x5<T>;
        (values: lodash.List<T>, array: lodash.List<T>): lodash.List<T>;
    }
    interface LodashPullAllBy1x2<T> {
        (iteratee: lodash.ValueIteratee<T>): LodashPullAllBy1x3<T>;
        (iteratee: lodash.__, array: ReadonlyArray<T>): LodashPullAllBy1x6<T>;
        (iteratee: lodash.ValueIteratee<T>, array: ReadonlyArray<T>): T[];
        (iteratee: lodash.__, array: lodash.List<T>): LodashPullAllBy2x6<T>;
        (iteratee: lodash.ValueIteratee<T>, array: lodash.List<T>): lodash.List<T>;
    }
    interface LodashPullAllBy1x3<T> {
        (array: ReadonlyArray<T>): T[];
        (array: lodash.List<T>): lodash.List<T>;
    }
    interface LodashPullAllBy1x4<T> {
        (iteratee: lodash.ValueIteratee<T>): LodashPullAllBy1x5<T>;
        (iteratee: lodash.__, values: lodash.List<T>): LodashPullAllBy1x6<T>;
        (iteratee: lodash.ValueIteratee<T>, values: lodash.List<T>): T[];
    }
    type LodashPullAllBy1x5<T> = (values: lodash.List<T>) => T[];
    type LodashPullAllBy1x6<T> = (iteratee: lodash.ValueIteratee<T>) => T[];
    interface LodashPullAllBy2x4<T> {
        (iteratee: lodash.ValueIteratee<T>): LodashPullAllBy2x5<T>;
        (iteratee: lodash.__, values: lodash.List<T>): LodashPullAllBy2x6<T>;
        (iteratee: lodash.ValueIteratee<T>, values: lodash.List<T>): lodash.List<T>;
    }
    type LodashPullAllBy2x5<T> = (values: lodash.List<T>) => lodash.List<T>;
    type LodashPullAllBy2x6<T> = (iteratee: lodash.ValueIteratee<T>) => lodash.List<T>;
    interface LodashPullAllBy3x1<T1, T2> {
        (values: lodash.List<T2>): LodashPullAllBy3x3<T1>;
        (values: lodash.__, array: ReadonlyArray<T1>): LodashPullAllBy3x5<T1, T2>;
        (values: lodash.List<T2>, array: ReadonlyArray<T1>): T1[];
        (values: lodash.__, array: lodash.List<T1>): LodashPullAllBy4x5<T1, T2>;
        (values: lodash.List<T2>, array: lodash.List<T1>): lodash.List<T1>;
    }
    interface LodashPullAllBy3x2<T2> {
        <T1>(iteratee: lodash.ValueIteratee<T1 | T2>): LodashPullAllBy3x3<T1>;
        <T1>(iteratee: lodash.__, array: ReadonlyArray<T1>): LodashPullAllBy3x6<T1, T2>;
        <T1>(iteratee: lodash.ValueIteratee<T1 | T2>, array: ReadonlyArray<T1>): T1[];
        <T1>(iteratee: lodash.__, array: lodash.List<T1>): LodashPullAllBy4x6<T1, T2>;
        <T1>(iteratee: lodash.ValueIteratee<T1 | T2>, array: lodash.List<T1>): lodash.List<T1>;
    }
    interface LodashPullAllBy3x3<T1> {
        (array: ReadonlyArray<T1>): T1[];
        (array: lodash.List<T1>): lodash.List<T1>;
    }
    interface LodashPullAllBy3x4<T1> {
        <T2>(iteratee: lodash.ValueIteratee<T1 | T2>): LodashPullAllBy3x5<T1, T2>;
        <T2>(iteratee: lodash.__, values: lodash.List<T2>): LodashPullAllBy3x6<T1, T2>;
        <T2>(iteratee: lodash.ValueIteratee<T1 | T2>, values: lodash.List<T2>): T1[];
    }
    type LodashPullAllBy3x5<T1, T2> = (values: lodash.List<T2>) => T1[];
    type LodashPullAllBy3x6<T1, T2> = (iteratee: lodash.ValueIteratee<T1 | T2>) => T1[];
    interface LodashPullAllBy4x4<T1> {
        <T2>(iteratee: lodash.ValueIteratee<T1 | T2>): LodashPullAllBy4x5<T1, T2>;
        <T2>(iteratee: lodash.__, values: lodash.List<T2>): LodashPullAllBy4x6<T1, T2>;
        <T2>(iteratee: lodash.ValueIteratee<T1 | T2>, values: lodash.List<T2>): lodash.List<T1>;
    }
    type LodashPullAllBy4x5<T1, T2> = (values: lodash.List<T2>) => lodash.List<T1>;
    type LodashPullAllBy4x6<T1, T2> = (iteratee: lodash.ValueIteratee<T1 | T2>) => lodash.List<T1>;
    interface LodashPullAllWith {
        <T>(comparator: lodash.Comparator<T>): LodashPullAllWith1x1<T>;
        <T>(comparator: lodash.__, values: lodash.List<T>): LodashPullAllWith1x2<T>;
        <T>(comparator: lodash.Comparator<T>, values: lodash.List<T>): LodashPullAllWith1x3<T>;
        <T>(comparator: lodash.__, values: lodash.__, array: ReadonlyArray<T>): LodashPullAllWith1x4<T>;
        <T>(comparator: lodash.Comparator<T>, values: lodash.__, array: ReadonlyArray<T>): LodashPullAllWith1x5<T>;
        <T>(comparator: lodash.__, values: lodash.List<T>, array: ReadonlyArray<T>): LodashPullAllWith1x6<T>;
        <T>(comparator: lodash.Comparator<T>, values: lodash.List<T>, array: ReadonlyArray<T>): T[];
        <T>(comparator: lodash.__, values: lodash.__, array: lodash.List<T>): LodashPullAllWith2x4<T>;
        <T>(comparator: lodash.Comparator<T>, values: lodash.__, array: lodash.List<T>): LodashPullAllWith2x5<T>;
        <T>(comparator: lodash.__, values: lodash.List<T>, array: lodash.List<T>): LodashPullAllWith2x6<T>;
        <T>(comparator: lodash.Comparator<T>, values: lodash.List<T>, array: lodash.List<T>): lodash.List<T>;
        <T1, T2>(comparator: lodash.Comparator2<T1, T2>): LodashPullAllWith3x1<T1, T2>;
        <T2>(comparator: lodash.__, values: lodash.List<T2>): LodashPullAllWith3x2<T2>;
        <T1, T2>(comparator: lodash.Comparator2<T1, T2>, values: lodash.List<T2>): LodashPullAllWith3x3<T1>;
        <T1>(comparator: lodash.__, values: lodash.__, array: ReadonlyArray<T1>): LodashPullAllWith3x4<T1>;
        <T1, T2>(comparator: lodash.Comparator2<T1, T2>, values: lodash.__, array: ReadonlyArray<T1>): LodashPullAllWith3x5<T1, T2>;
        <T1, T2>(comparator: lodash.__, values: lodash.List<T2>, array: ReadonlyArray<T1>): LodashPullAllWith3x6<T1, T2>;
        <T1, T2>(comparator: lodash.Comparator2<T1, T2>, values: lodash.List<T2>, array: ReadonlyArray<T1>): T1[];
        <T1>(comparator: lodash.__, values: lodash.__, array: lodash.List<T1>): LodashPullAllWith4x4<T1>;
        <T1, T2>(comparator: lodash.Comparator2<T1, T2>, values: lodash.__, array: lodash.List<T1>): LodashPullAllWith4x5<T1, T2>;
        <T1, T2>(comparator: lodash.__, values: lodash.List<T2>, array: lodash.List<T1>): LodashPullAllWith4x6<T1, T2>;
        <T1, T2>(comparator: lodash.Comparator2<T1, T2>, values: lodash.List<T2>, array: lodash.List<T1>): lodash.List<T1>;
    }
    interface LodashPullAllWith1x1<T> {
        (values: lodash.List<T>): LodashPullAllWith1x3<T>;
        (values: lodash.__, array: ReadonlyArray<T>): LodashPullAllWith1x5<T>;
        (values: lodash.List<T>, array: ReadonlyArray<T>): T[];
        (values: lodash.__, array: lodash.List<T>): LodashPullAllWith2x5<T>;
        (values: lodash.List<T>, array: lodash.List<T>): lodash.List<T>;
    }
    interface LodashPullAllWith1x2<T> {
        (comparator: lodash.Comparator<T>): LodashPullAllWith1x3<T>;
        (comparator: lodash.__, array: ReadonlyArray<T>): LodashPullAllWith1x6<T>;
        (comparator: lodash.Comparator<T>, array: ReadonlyArray<T>): T[];
        (comparator: lodash.__, array: lodash.List<T>): LodashPullAllWith2x6<T>;
        (comparator: lodash.Comparator<T>, array: lodash.List<T>): lodash.List<T>;
    }
    interface LodashPullAllWith1x3<T> {
        (array: ReadonlyArray<T>): T[];
        (array: lodash.List<T>): lodash.List<T>;
    }
    interface LodashPullAllWith1x4<T> {
        (comparator: lodash.Comparator<T>): LodashPullAllWith1x5<T>;
        (comparator: lodash.__, values: lodash.List<T>): LodashPullAllWith1x6<T>;
        (comparator: lodash.Comparator<T>, values: lodash.List<T>): T[];
    }
    type LodashPullAllWith1x5<T> = (values: lodash.List<T>) => T[];
    type LodashPullAllWith1x6<T> = (comparator: lodash.Comparator<T>) => T[];
    interface LodashPullAllWith2x4<T> {
        (comparator: lodash.Comparator<T>): LodashPullAllWith2x5<T>;
        (comparator: lodash.__, values: lodash.List<T>): LodashPullAllWith2x6<T>;
        (comparator: lodash.Comparator<T>, values: lodash.List<T>): lodash.List<T>;
    }
    type LodashPullAllWith2x5<T> = (values: lodash.List<T>) => lodash.List<T>;
    type LodashPullAllWith2x6<T> = (comparator: lodash.Comparator<T>) => lodash.List<T>;
    interface LodashPullAllWith3x1<T1, T2> {
        (values: lodash.List<T2>): LodashPullAllWith3x3<T1>;
        (values: lodash.__, array: ReadonlyArray<T1>): LodashPullAllWith3x5<T1, T2>;
        (values: lodash.List<T2>, array: ReadonlyArray<T1>): T1[];
        (values: lodash.__, array: lodash.List<T1>): LodashPullAllWith4x5<T1, T2>;
        (values: lodash.List<T2>, array: lodash.List<T1>): lodash.List<T1>;
    }
    interface LodashPullAllWith3x2<T2> {
        <T1>(comparator: lodash.Comparator2<T1, T2>): LodashPullAllWith3x3<T1>;
        <T1>(comparator: lodash.__, array: ReadonlyArray<T1>): LodashPullAllWith3x6<T1, T2>;
        <T1>(comparator: lodash.Comparator2<T1, T2>, array: ReadonlyArray<T1>): T1[];
        <T1>(comparator: lodash.__, array: lodash.List<T1>): LodashPullAllWith4x6<T1, T2>;
        <T1>(comparator: lodash.Comparator2<T1, T2>, array: lodash.List<T1>): lodash.List<T1>;
    }
    interface LodashPullAllWith3x3<T1> {
        (array: ReadonlyArray<T1>): T1[];
        (array: lodash.List<T1>): lodash.List<T1>;
    }
    interface LodashPullAllWith3x4<T1> {
        <T2>(comparator: lodash.Comparator2<T1, T2>): LodashPullAllWith3x5<T1, T2>;
        <T2>(comparator: lodash.__, values: lodash.List<T2>): LodashPullAllWith3x6<T1, T2>;
        <T2>(comparator: lodash.Comparator2<T1, T2>, values: lodash.List<T2>): T1[];
    }
    type LodashPullAllWith3x5<T1, T2> = (values: lodash.List<T2>) => T1[];
    type LodashPullAllWith3x6<T1, T2> = (comparator: lodash.Comparator2<T1, T2>) => T1[];
    interface LodashPullAllWith4x4<T1> {
        <T2>(comparator: lodash.Comparator2<T1, T2>): LodashPullAllWith4x5<T1, T2>;
        <T2>(comparator: lodash.__, values: lodash.List<T2>): LodashPullAllWith4x6<T1, T2>;
        <T2>(comparator: lodash.Comparator2<T1, T2>, values: lodash.List<T2>): lodash.List<T1>;
    }
    type LodashPullAllWith4x5<T1, T2> = (values: lodash.List<T2>) => lodash.List<T1>;
    type LodashPullAllWith4x6<T1, T2> = (comparator: lodash.Comparator2<T1, T2>) => lodash.List<T1>;
    interface LodashPullAt {
        (indexes: lodash.Many<number>): LodashPullAt1x1;
        <T>(indexes: lodash.__, array: ReadonlyArray<T>): LodashPullAt1x2<T>;
        <T>(indexes: lodash.Many<number>, array: ReadonlyArray<T>): T[];
        <T>(indexes: lodash.__, array: lodash.List<T>): LodashPullAt2x2<T>;
        <T>(indexes: lodash.Many<number>, array: lodash.List<T>): lodash.List<T>;
    }
    interface LodashPullAt1x1 {
        <T>(array: ReadonlyArray<T>): T[];
        <T>(array: lodash.List<T>): lodash.List<T>;
    }
    type LodashPullAt1x2<T> = (indexes: lodash.Many<number>) => T[];
    type LodashPullAt2x2<T> = (indexes: lodash.Many<number>) => lodash.List<T>;
    interface LodashRandom {
        (maxOrMin: number): LodashRandom1x1;
        (max: lodash.__, floating: boolean): LodashRandom1x2;
        (maxOrMin: number, floatingOrMax: boolean | number): number;
        (min: lodash.__, max: number): LodashRandom2x2;
    }
    type LodashRandom1x1 = (floatingOrMax: boolean | number) => number;
    type LodashRandom1x2 = (max: number) => number;
    type LodashRandom2x2 = (min: number) => number;
    interface LodashRange {
        (start: number): LodashRange1x1;
        (start: lodash.__, end: number): LodashRange1x2;
        (start: number, end: number): number[];
    }
    type LodashRange1x1 = (end: number) => number[];
    type LodashRange1x2 = (start: number) => number[];
    interface LodashRangeRight {
        (start: number): LodashRangeRight1x1;
        (start: lodash.__, end: number): LodashRangeRight1x2;
        (start: number, end: number): number[];
    }
    type LodashRangeRight1x1 = (end: number) => number[];
    type LodashRangeRight1x2 = (start: number) => number[];
    interface LodashRangeStep {
        (start: number): LodashRangeStep1x1;
        (start: lodash.__, end: number): LodashRangeStep1x2;
        (start: number, end: number): LodashRangeStep1x3;
        (start: lodash.__, end: lodash.__, step: number): LodashRangeStep1x4;
        (start: number, end: lodash.__, step: number): LodashRangeStep1x5;
        (start: lodash.__, end: number, step: number): LodashRangeStep1x6;
        (start: number, end: number, step: number): number[];
    }
    interface LodashRangeStep1x1 {
        (end: number): LodashRangeStep1x3;
        (end: lodash.__, step: number): LodashRangeStep1x5;
        (end: number, step: number): number[];
    }
    interface LodashRangeStep1x2 {
        (start: number): LodashRangeStep1x3;
        (start: lodash.__, step: number): LodashRangeStep1x6;
        (start: number, step: number): number[];
    }
    type LodashRangeStep1x3 = (step: number) => number[];
    interface LodashRangeStep1x4 {
        (start: number): LodashRangeStep1x5;
        (start: lodash.__, end: number): LodashRangeStep1x6;
        (start: number, end: number): number[];
    }
    type LodashRangeStep1x5 = (end: number) => number[];
    type LodashRangeStep1x6 = (start: number) => number[];
    interface LodashRangeStepRight {
        (start: number): LodashRangeStepRight1x1;
        (start: lodash.__, end: number): LodashRangeStepRight1x2;
        (start: number, end: number): LodashRangeStepRight1x3;
        (start: lodash.__, end: lodash.__, step: number): LodashRangeStepRight1x4;
        (start: number, end: lodash.__, step: number): LodashRangeStepRight1x5;
        (start: lodash.__, end: number, step: number): LodashRangeStepRight1x6;
        (start: number, end: number, step: number): number[];
    }
    interface LodashRangeStepRight1x1 {
        (end: number): LodashRangeStepRight1x3;
        (end: lodash.__, step: number): LodashRangeStepRight1x5;
        (end: number, step: number): number[];
    }
    interface LodashRangeStepRight1x2 {
        (start: number): LodashRangeStepRight1x3;
        (start: lodash.__, step: number): LodashRangeStepRight1x6;
        (start: number, step: number): number[];
    }
    type LodashRangeStepRight1x3 = (step: number) => number[];
    interface LodashRangeStepRight1x4 {
        (start: number): LodashRangeStepRight1x5;
        (start: lodash.__, end: number): LodashRangeStepRight1x6;
        (start: number, end: number): number[];
    }
    type LodashRangeStepRight1x5 = (end: number) => number[];
    type LodashRangeStepRight1x6 = (start: number) => number[];
    interface LodashRearg {
        (indexes: lodash.Many<number>): LodashRearg1x1;
        (indexes: lodash.__, func: (...args: any[]) => any): LodashRearg1x2;
        (indexes: lodash.Many<number>, func: (...args: any[]) => any): (...args: any[]) => any;
    }
    type LodashRearg1x1 = (func: (...args: any[]) => any) => (...args: any[]) => any;
    type LodashRearg1x2 = (indexes: lodash.Many<number>) => (...args: any[]) => any;
    interface LodashReduce {
        <T, TResult>(callback: lodash.MemoIteratorCapped<T, TResult>): LodashReduce1x1<T, TResult>;
        <TResult>(callback: lodash.__, accumulator: TResult): LodashReduce1x2<TResult>;
        <T, TResult>(callback: lodash.MemoIteratorCapped<T, TResult>, accumulator: TResult): LodashReduce1x3<T, TResult>;
        <T>(callback: lodash.__, accumulator: lodash.__, collection: T[] | null | undefined): LodashReduce1x4<T>;
        <T, TResult>(callback: lodash.MemoIteratorCapped<T, TResult>, accumulator: lodash.__, collection: T[] | null | undefined): LodashReduce1x5<TResult>;
        <T, TResult>(callback: lodash.__, accumulator: TResult, collection: T[] | null | undefined): LodashReduce1x6<T, TResult>;
        <T, TResult>(callback: lodash.MemoIteratorCapped<T, TResult>, accumulator: TResult, collection: T[] | lodash.List<T> | null | undefined): TResult;
        <T>(callback: lodash.__, accumulator: lodash.__, collection: lodash.List<T> | null | undefined): LodashReduce2x4<T>;
        <T, TResult>(callback: lodash.MemoIteratorCapped<T, TResult>, accumulator: lodash.__, collection: lodash.List<T> | null | undefined): LodashReduce2x5<TResult>;
        <T, TResult>(callback: lodash.__, accumulator: TResult, collection: lodash.List<T> | null | undefined): LodashReduce2x6<T, TResult>;
        <T extends object, TResult>(callback: lodash.MemoIteratorCapped<T[keyof T], TResult>): LodashReduce3x1<T, TResult>;
        <T extends object, TResult>(callback: lodash.MemoIteratorCapped<T[keyof T], TResult>, accumulator: TResult): LodashReduce3x3<T, TResult>;
        <T extends object>(callback: lodash.__, accumulator: lodash.__, collection: T | null | undefined): LodashReduce3x4<T>;
        <T extends object, TResult>(callback: lodash.MemoIteratorCapped<T[keyof T], TResult>, accumulator: lodash.__, collection: T | null | undefined): LodashReduce3x5<TResult>;
        <T extends object, TResult>(callback: lodash.__, accumulator: TResult, collection: T | null | undefined): LodashReduce3x6<T, TResult>;
        <T extends object, TResult>(callback: lodash.MemoIteratorCapped<T[keyof T], TResult>, accumulator: TResult, collection: T | null | undefined): TResult;
    }
    interface LodashReduce1x1<T, TResult> {
        (accumulator: TResult): LodashReduce1x3<T, TResult>;
        (accumulator: lodash.__, collection: T[] | null | undefined): LodashReduce1x5<TResult>;
        (accumulator: TResult, collection: T[] | lodash.List<T> | null | undefined): TResult;
        (accumulator: lodash.__, collection: lodash.List<T> | null | undefined): LodashReduce2x5<TResult>;
    }
    interface LodashReduce1x2<TResult> {
        <T>(callback: lodash.MemoIteratorCapped<T, TResult>): LodashReduce1x3<T, TResult>;
        <T>(callback: lodash.__, collection: T[] | null | undefined): LodashReduce1x6<T, TResult>;
        <T>(callback: lodash.MemoIteratorCapped<T, TResult>, collection: T[] | lodash.List<T> | null | undefined): TResult;
        <T>(callback: lodash.__, collection: lodash.List<T> | null | undefined): LodashReduce2x6<T, TResult>;
        <T extends object>(callback: lodash.MemoIteratorCapped<T[keyof T], TResult>): LodashReduce3x3<T, TResult>;
        <T extends object>(callback: lodash.__, collection: T | null | undefined): LodashReduce3x6<T, TResult>;
        <T extends object>(callback: lodash.MemoIteratorCapped<T[keyof T], TResult>, collection: T | null | undefined): TResult;
    }
    type LodashReduce1x3<T, TResult> = (collection: T[] | lodash.List<T> | null | undefined) => TResult;
    interface LodashReduce1x4<T> {
        <TResult>(callback: lodash.MemoIteratorCapped<T, TResult>): LodashReduce1x5<TResult>;
        <TResult>(callback: lodash.__, accumulator: TResult): LodashReduce1x6<T, TResult>;
        <TResult>(callback: lodash.MemoIteratorCapped<T, TResult>, accumulator: TResult): TResult;
    }
    type LodashReduce1x5<TResult> = (accumulator: TResult) => TResult;
    type LodashReduce1x6<T, TResult> = (callback: lodash.MemoIteratorCapped<T, TResult>) => TResult;
    interface LodashReduce2x4<T> {
        <TResult>(callback: lodash.MemoIteratorCapped<T, TResult>): LodashReduce2x5<TResult>;
        <TResult>(callback: lodash.__, accumulator: TResult): LodashReduce2x6<T, TResult>;
        <TResult>(callback: lodash.MemoIteratorCapped<T, TResult>, accumulator: TResult): TResult;
    }
    type LodashReduce2x5<TResult> = (accumulator: TResult) => TResult;
    type LodashReduce2x6<T, TResult> = (callback: lodash.MemoIteratorCapped<T, TResult>) => TResult;
    interface LodashReduce3x1<T, TResult> {
        (accumulator: TResult): LodashReduce3x3<T, TResult>;
        (accumulator: lodash.__, collection: T | null | undefined): LodashReduce3x5<TResult>;
        (accumulator: TResult, collection: T | null | undefined): TResult;
    }
    type LodashReduce3x3<T, TResult> = (collection: T | null | undefined) => TResult;
    interface LodashReduce3x4<T> {
        <TResult>(callback: lodash.MemoIteratorCapped<T[keyof T], TResult>): LodashReduce3x5<TResult>;
        <TResult>(callback: lodash.__, accumulator: TResult): LodashReduce3x6<T, TResult>;
        <TResult>(callback: lodash.MemoIteratorCapped<T[keyof T], TResult>, accumulator: TResult): TResult;
    }
    type LodashReduce3x5<TResult> = (accumulator: TResult) => TResult;
    type LodashReduce3x6<T, TResult> = (callback: lodash.MemoIteratorCapped<T[keyof T], TResult>) => TResult;
    interface LodashReduceRight {
        <T, TResult>(callback: lodash.MemoIteratorCappedRight<T, TResult>): LodashReduceRight1x1<T, TResult>;
        <TResult>(callback: lodash.__, accumulator: TResult): LodashReduceRight1x2<TResult>;
        <T, TResult>(callback: lodash.MemoIteratorCappedRight<T, TResult>, accumulator: TResult): LodashReduceRight1x3<T, TResult>;
        <T>(callback: lodash.__, accumulator: lodash.__, collection: T[] | null | undefined): LodashReduceRight1x4<T>;
        <T, TResult>(callback: lodash.MemoIteratorCappedRight<T, TResult>, accumulator: lodash.__, collection: T[] | null | undefined): LodashReduceRight1x5<TResult>;
        <T, TResult>(callback: lodash.__, accumulator: TResult, collection: T[] | null | undefined): LodashReduceRight1x6<T, TResult>;
        <T, TResult>(callback: lodash.MemoIteratorCappedRight<T, TResult>, accumulator: TResult, collection: T[] | lodash.List<T> | null | undefined): TResult;
        <T>(callback: lodash.__, accumulator: lodash.__, collection: lodash.List<T> | null | undefined): LodashReduceRight2x4<T>;
        <T, TResult>(callback: lodash.MemoIteratorCappedRight<T, TResult>, accumulator: lodash.__, collection: lodash.List<T> | null | undefined): LodashReduceRight2x5<TResult>;
        <T, TResult>(callback: lodash.__, accumulator: TResult, collection: lodash.List<T> | null | undefined): LodashReduceRight2x6<T, TResult>;
        <T extends object, TResult>(callback: lodash.MemoIteratorCappedRight<T[keyof T], TResult>): LodashReduceRight3x1<T, TResult>;
        <T extends object, TResult>(callback: lodash.MemoIteratorCappedRight<T[keyof T], TResult>, accumulator: TResult): LodashReduceRight3x3<T, TResult>;
        <T extends object>(callback: lodash.__, accumulator: lodash.__, collection: T | null | undefined): LodashReduceRight3x4<T>;
        <T extends object, TResult>(callback: lodash.MemoIteratorCappedRight<T[keyof T], TResult>, accumulator: lodash.__, collection: T | null | undefined): LodashReduceRight3x5<TResult>;
        <T extends object, TResult>(callback: lodash.__, accumulator: TResult, collection: T | null | undefined): LodashReduceRight3x6<T, TResult>;
        <T extends object, TResult>(callback: lodash.MemoIteratorCappedRight<T[keyof T], TResult>, accumulator: TResult, collection: T | null | undefined): TResult;
    }
    interface LodashReduceRight1x1<T, TResult> {
        (accumulator: TResult): LodashReduceRight1x3<T, TResult>;
        (accumulator: lodash.__, collection: T[] | null | undefined): LodashReduceRight1x5<TResult>;
        (accumulator: TResult, collection: T[] | lodash.List<T> | null | undefined): TResult;
        (accumulator: lodash.__, collection: lodash.List<T> | null | undefined): LodashReduceRight2x5<TResult>;
    }
    interface LodashReduceRight1x2<TResult> {
        <T>(callback: lodash.MemoIteratorCappedRight<T, TResult>): LodashReduceRight1x3<T, TResult>;
        <T>(callback: lodash.__, collection: T[] | null | undefined): LodashReduceRight1x6<T, TResult>;
        <T>(callback: lodash.MemoIteratorCappedRight<T, TResult>, collection: T[] | lodash.List<T> | null | undefined): TResult;
        <T>(callback: lodash.__, collection: lodash.List<T> | null | undefined): LodashReduceRight2x6<T, TResult>;
        <T extends object>(callback: lodash.MemoIteratorCappedRight<T[keyof T], TResult>): LodashReduceRight3x3<T, TResult>;
        <T extends object>(callback: lodash.__, collection: T | null | undefined): LodashReduceRight3x6<T, TResult>;
        <T extends object>(callback: lodash.MemoIteratorCappedRight<T[keyof T], TResult>, collection: T | null | undefined): TResult;
    }
    type LodashReduceRight1x3<T, TResult> = (collection: T[] | lodash.List<T> | null | undefined) => TResult;
    interface LodashReduceRight1x4<T> {
        <TResult>(callback: lodash.MemoIteratorCappedRight<T, TResult>): LodashReduceRight1x5<TResult>;
        <TResult>(callback: lodash.__, accumulator: TResult): LodashReduceRight1x6<T, TResult>;
        <TResult>(callback: lodash.MemoIteratorCappedRight<T, TResult>, accumulator: TResult): TResult;
    }
    type LodashReduceRight1x5<TResult> = (accumulator: TResult) => TResult;
    type LodashReduceRight1x6<T, TResult> = (callback: lodash.MemoIteratorCappedRight<T, TResult>) => TResult;
    interface LodashReduceRight2x4<T> {
        <TResult>(callback: lodash.MemoIteratorCappedRight<T, TResult>): LodashReduceRight2x5<TResult>;
        <TResult>(callback: lodash.__, accumulator: TResult): LodashReduceRight2x6<T, TResult>;
        <TResult>(callback: lodash.MemoIteratorCappedRight<T, TResult>, accumulator: TResult): TResult;
    }
    type LodashReduceRight2x5<TResult> = (accumulator: TResult) => TResult;
    type LodashReduceRight2x6<T, TResult> = (callback: lodash.MemoIteratorCappedRight<T, TResult>) => TResult;
    interface LodashReduceRight3x1<T, TResult> {
        (accumulator: TResult): LodashReduceRight3x3<T, TResult>;
        (accumulator: lodash.__, collection: T | null | undefined): LodashReduceRight3x5<TResult>;
        (accumulator: TResult, collection: T | null | undefined): TResult;
    }
    type LodashReduceRight3x3<T, TResult> = (collection: T | null | undefined) => TResult;
    interface LodashReduceRight3x4<T> {
        <TResult>(callback: lodash.MemoIteratorCappedRight<T[keyof T], TResult>): LodashReduceRight3x5<TResult>;
        <TResult>(callback: lodash.__, accumulator: TResult): LodashReduceRight3x6<T, TResult>;
        <TResult>(callback: lodash.MemoIteratorCappedRight<T[keyof T], TResult>, accumulator: TResult): TResult;
    }
    type LodashReduceRight3x5<TResult> = (accumulator: TResult) => TResult;
    type LodashReduceRight3x6<T, TResult> = (callback: lodash.MemoIteratorCappedRight<T[keyof T], TResult>) => TResult;
    interface LodashReject {
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>): LodashReject1x1<T>;
        <T>(predicate: lodash.__, collection: lodash.List<T> | null | undefined): LodashReject1x2<T>;
        <T>(predicate: lodash.ValueIterateeCustom<T, boolean>, collection: lodash.List<T> | null | undefined): T[];
        <T extends object>(predicate: lodash.__, collection: T | null | undefined): LodashReject2x2<T>;
        <T extends object>(predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>, collection: T | null | undefined): Array<T[keyof T]>;
    }
    type LodashReject1x1<T> = (collection: lodash.List<T> | object | null | undefined) => T[];
    type LodashReject1x2<T> = (predicate: lodash.ValueIterateeCustom<T, boolean>) => T[];
    type LodashReject2x2<T> = (predicate: lodash.ValueIterateeCustom<T[keyof T], boolean>) => Array<T[keyof T]>;
    interface LodashRemove {
        <T>(predicate: lodash.ValueIteratee<T>): LodashRemove1x1<T>;
        <T>(predicate: lodash.__, array: lodash.List<T>): LodashRemove1x2<T>;
        <T>(predicate: lodash.ValueIteratee<T>, array: lodash.List<T>): T[];
    }
    type LodashRemove1x1<T> = (array: lodash.List<T>) => T[];
    type LodashRemove1x2<T> = (predicate: lodash.ValueIteratee<T>) => T[];
    interface LodashRepeat {
        (n: number): LodashRepeat1x1;
        (n: lodash.__, string: string): LodashRepeat1x2;
        (n: number, string: string): string;
    }
    type LodashRepeat1x1 = (string: string) => string;
    type LodashRepeat1x2 = (n: number) => string;
    interface LodashReplace {
        (pattern: RegExp | string): LodashReplace1x1;
        (pattern: lodash.__, replacement: lodash.ReplaceFunction | string): LodashReplace1x2;
        (pattern: RegExp | string, replacement: lodash.ReplaceFunction | string): LodashReplace1x3;
        (pattern: lodash.__, replacement: lodash.__, string: string): LodashReplace1x4;
        (pattern: RegExp | string, replacement: lodash.__, string: string): LodashReplace1x5;
        (pattern: lodash.__, replacement: lodash.ReplaceFunction | string, string: string): LodashReplace1x6;
        (pattern: RegExp | string, replacement: lodash.ReplaceFunction | string, string: string): string;
    }
    interface LodashReplace1x1 {
        (replacement: lodash.ReplaceFunction | string): LodashReplace1x3;
        (replacement: lodash.__, string: string): LodashReplace1x5;
        (replacement: lodash.ReplaceFunction | string, string: string): string;
    }
    interface LodashReplace1x2 {
        (pattern: RegExp | string): LodashReplace1x3;
        (pattern: lodash.__, string: string): LodashReplace1x6;
        (pattern: RegExp | string, string: string): string;
    }
    type LodashReplace1x3 = (string: string) => string;
    interface LodashReplace1x4 {
        (pattern: RegExp | string): LodashReplace1x5;
        (pattern: lodash.__, replacement: lodash.ReplaceFunction | string): LodashReplace1x6;
        (pattern: RegExp | string, replacement: lodash.ReplaceFunction | string): string;
    }
    type LodashReplace1x5 = (replacement: lodash.ReplaceFunction | string) => string;
    type LodashReplace1x6 = (pattern: RegExp | string) => string;
    type LodashRest = (func: (...args: any[]) => any) => (...args: any[]) => any;
    interface LodashRestFrom {
        (start: number): LodashRestFrom1x1;
        (start: lodash.__, func: (...args: any[]) => any): LodashRestFrom1x2;
        (start: number, func: (...args: any[]) => any): (...args: any[]) => any;
    }
    type LodashRestFrom1x1 = (func: (...args: any[]) => any) => (...args: any[]) => any;
    type LodashRestFrom1x2 = (start: number) => (...args: any[]) => any;
    interface LodashResult {
        (path: lodash.PropertyPath): LodashResult1x1;
        (path: lodash.__, object: any): LodashResult1x2;
        <TResult>(path: lodash.PropertyPath, object: any): TResult;
    }
    type LodashResult1x1 = <TResult>(object: any) => TResult;
    type LodashResult1x2 = <TResult>(path: lodash.PropertyPath) => TResult;
    type LodashReverse = <TList extends lodash.List<any>>(array: TList) => TList;
    type LodashRound = (n: number) => number;
    type LodashRunInContext = (context: object) => lodash.LoDashStatic;
    interface LodashSample {
        <T>(collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): T | undefined;
        <T extends object>(collection: T | null | undefined): T[keyof T] | undefined;
    }
    interface LodashSampleSize {
        (n: number): LodashSampleSize1x1;
        <T>(n: lodash.__, collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): LodashSampleSize1x2<T>;
        <T>(n: number, collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): T[];
        <T extends object>(n: lodash.__, collection: T | null | undefined): LodashSampleSize2x2<T>;
        <T extends object>(n: number, collection: T | null | undefined): Array<T[keyof T]>;
    }
    interface LodashSampleSize1x1 {
        <T>(collection: lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): T[];
        <T extends object>(collection: T | null | undefined): Array<T[keyof T]>;
    }
    type LodashSampleSize1x2<T> = (n: number) => T[];
    type LodashSampleSize2x2<T> = (n: number) => Array<T[keyof T]>;
    interface LodashSetWith {
        <T extends object>(customizer: lodash.SetWithCustomizer<T>): LodashSetWith1x1<T>;
        (customizer: lodash.__, path: lodash.PropertyPath): LodashSetWith1x2;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.PropertyPath): LodashSetWith1x3<T>;
        (customizer: lodash.__, path: lodash.__, value: any): LodashSetWith1x4;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.__, value: any): LodashSetWith1x5<T>;
        (customizer: lodash.__, path: lodash.PropertyPath, value: any): LodashSetWith1x6;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.PropertyPath, value: any): LodashSetWith1x7<T>;
        <T extends object>(customizer: lodash.__, path: lodash.__, value: lodash.__, object: T): LodashSetWith1x8<T>;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.__, value: lodash.__, object: T): LodashSetWith1x9<T>;
        <T extends object>(customizer: lodash.__, path: lodash.PropertyPath, value: lodash.__, object: T): LodashSetWith1x10<T>;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.PropertyPath, value: lodash.__, object: T): LodashSetWith1x11<T>;
        <T extends object>(customizer: lodash.__, path: lodash.__, value: any, object: T): LodashSetWith1x12<T>;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.__, value: any, object: T): LodashSetWith1x13<T>;
        <T extends object>(customizer: lodash.__, path: lodash.PropertyPath, value: any, object: T): LodashSetWith1x14<T>;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.PropertyPath, value: any, object: T): T;
    }
    interface LodashSetWith1x1<T> {
        (path: lodash.PropertyPath): LodashSetWith1x3<T>;
        (path: lodash.__, value: any): LodashSetWith1x5<T>;
        (path: lodash.PropertyPath, value: any): LodashSetWith1x7<T>;
        (path: lodash.__, value: lodash.__, object: T): LodashSetWith1x9<T>;
        (path: lodash.PropertyPath, value: lodash.__, object: T): LodashSetWith1x11<T>;
        (path: lodash.__, value: any, object: T): LodashSetWith1x13<T>;
        (path: lodash.PropertyPath, value: any, object: T): T;
    }
    interface LodashSetWith1x2 {
        <T extends object>(customizer: lodash.SetWithCustomizer<T>): LodashSetWith1x3<T>;
        (customizer: lodash.__, value: any): LodashSetWith1x6;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, value: any): LodashSetWith1x7<T>;
        <T extends object>(customizer: lodash.__, value: lodash.__, object: T): LodashSetWith1x10<T>;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, value: lodash.__, object: T): LodashSetWith1x11<T>;
        <T extends object>(customizer: lodash.__, value: any, object: T): LodashSetWith1x14<T>;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, value: any, object: T): T;
    }
    interface LodashSetWith1x3<T> {
        (value: any): LodashSetWith1x7<T>;
        (value: lodash.__, object: T): LodashSetWith1x11<T>;
        (value: any, object: T): T;
    }
    interface LodashSetWith1x4 {
        <T extends object>(customizer: lodash.SetWithCustomizer<T>): LodashSetWith1x5<T>;
        (customizer: lodash.__, path: lodash.PropertyPath): LodashSetWith1x6;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.PropertyPath): LodashSetWith1x7<T>;
        <T extends object>(customizer: lodash.__, path: lodash.__, object: T): LodashSetWith1x12<T>;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.__, object: T): LodashSetWith1x13<T>;
        <T extends object>(customizer: lodash.__, path: lodash.PropertyPath, object: T): LodashSetWith1x14<T>;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.PropertyPath, object: T): T;
    }
    interface LodashSetWith1x5<T> {
        (path: lodash.PropertyPath): LodashSetWith1x7<T>;
        (path: lodash.__, object: T): LodashSetWith1x13<T>;
        (path: lodash.PropertyPath, object: T): T;
    }
    interface LodashSetWith1x6 {
        <T extends object>(customizer: lodash.SetWithCustomizer<T>): LodashSetWith1x7<T>;
        <T extends object>(customizer: lodash.__, object: T): LodashSetWith1x14<T>;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, object: T): T;
    }
    type LodashSetWith1x7<T> = (object: T) => T;
    interface LodashSetWith1x8<T> {
        (customizer: lodash.SetWithCustomizer<T>): LodashSetWith1x9<T>;
        (customizer: lodash.__, path: lodash.PropertyPath): LodashSetWith1x10<T>;
        (customizer: lodash.SetWithCustomizer<T>, path: lodash.PropertyPath): LodashSetWith1x11<T>;
        (customizer: lodash.__, path: lodash.__, value: any): LodashSetWith1x12<T>;
        (customizer: lodash.SetWithCustomizer<T>, path: lodash.__, value: any): LodashSetWith1x13<T>;
        (customizer: lodash.__, path: lodash.PropertyPath, value: any): LodashSetWith1x14<T>;
        (customizer: lodash.SetWithCustomizer<T>, path: lodash.PropertyPath, value: any): T;
    }
    interface LodashSetWith1x9<T> {
        (path: lodash.PropertyPath): LodashSetWith1x11<T>;
        (path: lodash.__, value: any): LodashSetWith1x13<T>;
        (path: lodash.PropertyPath, value: any): T;
    }
    interface LodashSetWith1x10<T> {
        (customizer: lodash.SetWithCustomizer<T>): LodashSetWith1x11<T>;
        (customizer: lodash.__, value: any): LodashSetWith1x14<T>;
        (customizer: lodash.SetWithCustomizer<T>, value: any): T;
    }
    type LodashSetWith1x11<T> = (value: any) => T;
    interface LodashSetWith1x12<T> {
        (customizer: lodash.SetWithCustomizer<T>): LodashSetWith1x13<T>;
        (customizer: lodash.__, path: lodash.PropertyPath): LodashSetWith1x14<T>;
        (customizer: lodash.SetWithCustomizer<T>, path: lodash.PropertyPath): T;
    }
    type LodashSetWith1x13<T> = (path: lodash.PropertyPath) => T;
    type LodashSetWith1x14<T> = (customizer: lodash.SetWithCustomizer<T>) => T;
    interface LodashShuffle {
        <T>(collection: lodash.List<T> | null | undefined): T[];
        <T extends object>(collection: T | null | undefined): Array<T[keyof T]>;
    }
    type LodashSize = (collection: object | string | null | undefined) => number;
    interface LodashSlice {
        (start: number): LodashSlice1x1;
        (start: lodash.__, end: number): LodashSlice1x2;
        (start: number, end: number): LodashSlice1x3;
        <T>(start: lodash.__, end: lodash.__, array: lodash.List<T> | null | undefined): LodashSlice1x4<T>;
        <T>(start: number, end: lodash.__, array: lodash.List<T> | null | undefined): LodashSlice1x5<T>;
        <T>(start: lodash.__, end: number, array: lodash.List<T> | null | undefined): LodashSlice1x6<T>;
        <T>(start: number, end: number, array: lodash.List<T> | null | undefined): T[];
    }
    interface LodashSlice1x1 {
        (end: number): LodashSlice1x3;
        <T>(end: lodash.__, array: lodash.List<T> | null | undefined): LodashSlice1x5<T>;
        <T>(end: number, array: lodash.List<T> | null | undefined): T[];
    }
    interface LodashSlice1x2 {
        (start: number): LodashSlice1x3;
        <T>(start: lodash.__, array: lodash.List<T> | null | undefined): LodashSlice1x6<T>;
        <T>(start: number, array: lodash.List<T> | null | undefined): T[];
    }
    type LodashSlice1x3 = <T>(array: lodash.List<T> | null | undefined) => T[];
    interface LodashSlice1x4<T> {
        (start: number): LodashSlice1x5<T>;
        (start: lodash.__, end: number): LodashSlice1x6<T>;
        (start: number, end: number): T[];
    }
    type LodashSlice1x5<T> = (end: number) => T[];
    type LodashSlice1x6<T> = (start: number) => T[];
    type LodashSnakeCase = (string: string) => string;
    interface LodashSortBy {
        <T>(iteratees: lodash.Many<lodash.ValueIteratee<T>>): LodashSortBy1x1<T>;
        <T>(iteratees: lodash.__, collection: lodash.List<T> | null | undefined): LodashSortBy1x2<T>;
        <T>(iteratees: lodash.Many<lodash.ValueIteratee<T>>, collection: lodash.List<T> | null | undefined): T[];
        <T extends object>(iteratees: lodash.__, collection: T | null | undefined): LodashSortBy2x2<T>;
        <T extends object>(iteratees: lodash.Many<lodash.ValueIteratee<T[keyof T]>>, collection: T | null | undefined): Array<T[keyof T]>;
    }
    type LodashSortBy1x1<T> = (collection: lodash.List<T> | object | null | undefined) => T[];
    type LodashSortBy1x2<T> = (iteratees: lodash.Many<lodash.ValueIteratee<T>>) => T[];
    type LodashSortBy2x2<T> = (iteratees: lodash.Many<lodash.ValueIteratee<T[keyof T]>>) => Array<T[keyof T]>;
    interface LodashSortedIndex {
        <T>(value: T): LodashSortedIndex1x1<T>;
        <T>(value: lodash.__, array: lodash.List<T> | null | undefined): LodashSortedIndex1x2<T>;
        <T>(value: T, array: lodash.List<T> | null | undefined): number;
    }
    type LodashSortedIndex1x1<T> = (array: lodash.List<T> | null | undefined) => number;
    type LodashSortedIndex1x2<T> = (value: T) => number;
    interface LodashSortedIndexBy {
        <T>(iteratee: lodash.ValueIteratee<T>): LodashSortedIndexBy1x1<T>;
        <T>(iteratee: lodash.__, value: T): LodashSortedIndexBy1x2<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, value: T): LodashSortedIndexBy1x3<T>;
        <T>(iteratee: lodash.__, value: lodash.__, array: lodash.List<T> | null | undefined): LodashSortedIndexBy1x4<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, value: lodash.__, array: lodash.List<T> | null | undefined): LodashSortedIndexBy1x5<T>;
        <T>(iteratee: lodash.__, value: T, array: lodash.List<T> | null | undefined): LodashSortedIndexBy1x6<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, value: T, array: lodash.List<T> | null | undefined): number;
    }
    interface LodashSortedIndexBy1x1<T> {
        (value: T): LodashSortedIndexBy1x3<T>;
        (value: lodash.__, array: lodash.List<T> | null | undefined): LodashSortedIndexBy1x5<T>;
        (value: T, array: lodash.List<T> | null | undefined): number;
    }
    interface LodashSortedIndexBy1x2<T> {
        (iteratee: lodash.ValueIteratee<T>): LodashSortedIndexBy1x3<T>;
        (iteratee: lodash.__, array: lodash.List<T> | null | undefined): LodashSortedIndexBy1x6<T>;
        (iteratee: lodash.ValueIteratee<T>, array: lodash.List<T> | null | undefined): number;
    }
    type LodashSortedIndexBy1x3<T> = (array: lodash.List<T> | null | undefined) => number;
    interface LodashSortedIndexBy1x4<T> {
        (iteratee: lodash.ValueIteratee<T>): LodashSortedIndexBy1x5<T>;
        (iteratee: lodash.__, value: T): LodashSortedIndexBy1x6<T>;
        (iteratee: lodash.ValueIteratee<T>, value: T): number;
    }
    type LodashSortedIndexBy1x5<T> = (value: T) => number;
    type LodashSortedIndexBy1x6<T> = (iteratee: lodash.ValueIteratee<T>) => number;
    interface LodashSortedIndexOf {
        <T>(value: T): LodashSortedIndexOf1x1<T>;
        <T>(value: lodash.__, array: lodash.List<T> | null | undefined): LodashSortedIndexOf1x2<T>;
        <T>(value: T, array: lodash.List<T> | null | undefined): number;
    }
    type LodashSortedIndexOf1x1<T> = (array: lodash.List<T> | null | undefined) => number;
    type LodashSortedIndexOf1x2<T> = (value: T) => number;
    interface LodashSortedLastIndex {
        <T>(value: T): LodashSortedLastIndex1x1<T>;
        <T>(value: lodash.__, array: lodash.List<T> | null | undefined): LodashSortedLastIndex1x2<T>;
        <T>(value: T, array: lodash.List<T> | null | undefined): number;
    }
    type LodashSortedLastIndex1x1<T> = (array: lodash.List<T> | null | undefined) => number;
    type LodashSortedLastIndex1x2<T> = (value: T) => number;
    interface LodashSortedLastIndexBy {
        <T>(iteratee: lodash.ValueIteratee<T>): LodashSortedLastIndexBy1x1<T>;
        <T>(iteratee: lodash.__, value: T): LodashSortedLastIndexBy1x2<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, value: T): LodashSortedLastIndexBy1x3<T>;
        <T>(iteratee: lodash.__, value: lodash.__, array: lodash.List<T> | null | undefined): LodashSortedLastIndexBy1x4<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, value: lodash.__, array: lodash.List<T> | null | undefined): LodashSortedLastIndexBy1x5<T>;
        <T>(iteratee: lodash.__, value: T, array: lodash.List<T> | null | undefined): LodashSortedLastIndexBy1x6<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, value: T, array: lodash.List<T> | null | undefined): number;
    }
    interface LodashSortedLastIndexBy1x1<T> {
        (value: T): LodashSortedLastIndexBy1x3<T>;
        (value: lodash.__, array: lodash.List<T> | null | undefined): LodashSortedLastIndexBy1x5<T>;
        (value: T, array: lodash.List<T> | null | undefined): number;
    }
    interface LodashSortedLastIndexBy1x2<T> {
        (iteratee: lodash.ValueIteratee<T>): LodashSortedLastIndexBy1x3<T>;
        (iteratee: lodash.__, array: lodash.List<T> | null | undefined): LodashSortedLastIndexBy1x6<T>;
        (iteratee: lodash.ValueIteratee<T>, array: lodash.List<T> | null | undefined): number;
    }
    type LodashSortedLastIndexBy1x3<T> = (array: lodash.List<T> | null | undefined) => number;
    interface LodashSortedLastIndexBy1x4<T> {
        (iteratee: lodash.ValueIteratee<T>): LodashSortedLastIndexBy1x5<T>;
        (iteratee: lodash.__, value: T): LodashSortedLastIndexBy1x6<T>;
        (iteratee: lodash.ValueIteratee<T>, value: T): number;
    }
    type LodashSortedLastIndexBy1x5<T> = (value: T) => number;
    type LodashSortedLastIndexBy1x6<T> = (iteratee: lodash.ValueIteratee<T>) => number;
    interface LodashSortedLastIndexOf {
        <T>(value: T): LodashSortedLastIndexOf1x1<T>;
        <T>(value: lodash.__, array: lodash.List<T> | null | undefined): LodashSortedLastIndexOf1x2<T>;
        <T>(value: T, array: lodash.List<T> | null | undefined): number;
    }
    type LodashSortedLastIndexOf1x1<T> = (array: lodash.List<T> | null | undefined) => number;
    type LodashSortedLastIndexOf1x2<T> = (value: T) => number;
    type LodashSortedUniq = <T>(array: lodash.List<T> | null | undefined) => T[];
    interface LodashSortedUniqBy {
        <T>(iteratee: lodash.ValueIteratee<T>): LodashSortedUniqBy1x1<T>;
        <T>(iteratee: lodash.__, array: lodash.List<T> | null | undefined): LodashSortedUniqBy1x2<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, array: lodash.List<T> | null | undefined): T[];
    }
    type LodashSortedUniqBy1x1<T> = (array: lodash.List<T> | null | undefined) => T[];
    type LodashSortedUniqBy1x2<T> = (iteratee: lodash.ValueIteratee<T>) => T[];
    interface LodashSplit {
        (separator: RegExp | string): LodashSplit1x1;
        (separator: lodash.__, string: string | null | undefined): LodashSplit1x2;
        (separator: RegExp | string, string: string | null | undefined): string[];
    }
    type LodashSplit1x1 = (string: string | null | undefined) => string[];
    type LodashSplit1x2 = (separator: RegExp | string) => string[];
    type LodashSpread = <TResult>(func: (...args: any[]) => TResult) => (...args: any[]) => TResult;
    interface LodashSpreadFrom {
        (start: number): LodashSpreadFrom1x1;
        <TResult>(start: lodash.__, func: (...args: any[]) => TResult): LodashSpreadFrom1x2<TResult>;
        <TResult>(start: number, func: (...args: any[]) => TResult): (...args: any[]) => TResult;
    }
    type LodashSpreadFrom1x1 = <TResult>(func: (...args: any[]) => TResult) => (...args: any[]) => TResult;
    type LodashSpreadFrom1x2<TResult> = (start: number) => (...args: any[]) => TResult;
    type LodashStartCase = (string: string) => string;
    interface LodashStartsWith {
        (target: string): LodashStartsWith1x1;
        (target: lodash.__, string: string): LodashStartsWith1x2;
        (target: string, string: string): boolean;
    }
    type LodashStartsWith1x1 = (string: string) => boolean;
    type LodashStartsWith1x2 = (target: string) => boolean;
    type LodashStubArray = () => any[];
    type LodashStubObject = () => any;
    type LodashStubString = () => string;
    type LodashStubTrue = () => true;
    interface LodashSubtract {
        (minuend: number): LodashSubtract1x1;
        (minuend: lodash.__, subtrahend: number): LodashSubtract1x2;
        (minuend: number, subtrahend: number): number;
    }
    type LodashSubtract1x1 = (subtrahend: number) => number;
    type LodashSubtract1x2 = (minuend: number) => number;
    type LodashSum = (collection: lodash.List<any> | null | undefined) => number;
    interface LodashSumBy {
        <T>(iteratee: ((value: T) => number) | string): LodashSumBy1x1<T>;
        <T>(iteratee: lodash.__, collection: lodash.List<T> | null | undefined): LodashSumBy1x2<T>;
        <T>(iteratee: ((value: T) => number) | string, collection: lodash.List<T> | null | undefined): number;
    }
    type LodashSumBy1x1<T> = (collection: lodash.List<T> | null | undefined) => number;
    type LodashSumBy1x2<T> = (iteratee: ((value: T) => number) | string) => number;
    interface LodashXor {
        <T>(arrays2: lodash.List<T> | null | undefined): LodashXor1x1<T>;
        <T>(arrays2: lodash.__, arrays: lodash.List<T> | null | undefined): LodashXor1x2<T>;
        <T>(arrays2: lodash.List<T> | null | undefined, arrays: lodash.List<T> | null | undefined): T[];
    }
    type LodashXor1x1<T> = (arrays: lodash.List<T> | null | undefined) => T[];
    type LodashXor1x2<T> = (arrays2: lodash.List<T> | null | undefined) => T[];
    interface LodashXorBy {
        <T>(iteratee: lodash.ValueIteratee<T>): LodashXorBy1x1<T>;
        <T>(iteratee: lodash.__, arrays: lodash.List<T> | null | undefined): LodashXorBy1x2<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, arrays: lodash.List<T> | null | undefined): LodashXorBy1x3<T>;
        <T>(iteratee: lodash.__, arrays: lodash.__, arrays2: lodash.List<T> | null | undefined): LodashXorBy1x4<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, arrays: lodash.__, arrays2: lodash.List<T> | null | undefined): LodashXorBy1x5<T>;
        <T>(iteratee: lodash.__, arrays: lodash.List<T> | null | undefined, arrays2: lodash.List<T> | null | undefined): LodashXorBy1x6<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, arrays: lodash.List<T> | null | undefined, arrays2: lodash.List<T> | null | undefined): T[];
    }
    interface LodashXorBy1x1<T> {
        (arrays: lodash.List<T> | null | undefined): LodashXorBy1x3<T>;
        (arrays: lodash.__, arrays2: lodash.List<T> | null | undefined): LodashXorBy1x5<T>;
        (arrays: lodash.List<T> | null | undefined, arrays2: lodash.List<T> | null | undefined): T[];
    }
    interface LodashXorBy1x2<T> {
        (iteratee: lodash.ValueIteratee<T>): LodashXorBy1x3<T>;
        (iteratee: lodash.__, arrays2: lodash.List<T> | null | undefined): LodashXorBy1x6<T>;
        (iteratee: lodash.ValueIteratee<T>, arrays2: lodash.List<T> | null | undefined): T[];
    }
    type LodashXorBy1x3<T> = (arrays2: lodash.List<T> | null | undefined) => T[];
    interface LodashXorBy1x4<T> {
        (iteratee: lodash.ValueIteratee<T>): LodashXorBy1x5<T>;
        (iteratee: lodash.__, arrays: lodash.List<T> | null | undefined): LodashXorBy1x6<T>;
        (iteratee: lodash.ValueIteratee<T>, arrays: lodash.List<T> | null | undefined): T[];
    }
    type LodashXorBy1x5<T> = (arrays: lodash.List<T> | null | undefined) => T[];
    type LodashXorBy1x6<T> = (iteratee: lodash.ValueIteratee<T>) => T[];
    interface LodashXorWith {
        <T>(comparator: lodash.Comparator<T>): LodashXorWith1x1<T>;
        <T>(comparator: lodash.__, arrays: lodash.List<T> | null | undefined): LodashXorWith1x2<T>;
        <T>(comparator: lodash.Comparator<T>, arrays: lodash.List<T> | null | undefined): LodashXorWith1x3<T>;
        <T>(comparator: lodash.__, arrays: lodash.__, arrays2: lodash.List<T> | null | undefined): LodashXorWith1x4<T>;
        <T>(comparator: lodash.Comparator<T>, arrays: lodash.__, arrays2: lodash.List<T> | null | undefined): LodashXorWith1x5<T>;
        <T>(comparator: lodash.__, arrays: lodash.List<T> | null | undefined, arrays2: lodash.List<T> | null | undefined): LodashXorWith1x6<T>;
        <T>(comparator: lodash.Comparator<T>, arrays: lodash.List<T> | null | undefined, arrays2: lodash.List<T> | null | undefined): T[];
    }
    interface LodashXorWith1x1<T> {
        (arrays: lodash.List<T> | null | undefined): LodashXorWith1x3<T>;
        (arrays: lodash.__, arrays2: lodash.List<T> | null | undefined): LodashXorWith1x5<T>;
        (arrays: lodash.List<T> | null | undefined, arrays2: lodash.List<T> | null | undefined): T[];
    }
    interface LodashXorWith1x2<T> {
        (comparator: lodash.Comparator<T>): LodashXorWith1x3<T>;
        (comparator: lodash.__, arrays2: lodash.List<T> | null | undefined): LodashXorWith1x6<T>;
        (comparator: lodash.Comparator<T>, arrays2: lodash.List<T> | null | undefined): T[];
    }
    type LodashXorWith1x3<T> = (arrays2: lodash.List<T> | null | undefined) => T[];
    interface LodashXorWith1x4<T> {
        (comparator: lodash.Comparator<T>): LodashXorWith1x5<T>;
        (comparator: lodash.__, arrays: lodash.List<T> | null | undefined): LodashXorWith1x6<T>;
        (comparator: lodash.Comparator<T>, arrays: lodash.List<T> | null | undefined): T[];
    }
    type LodashXorWith1x5<T> = (arrays: lodash.List<T> | null | undefined) => T[];
    type LodashXorWith1x6<T> = (comparator: lodash.Comparator<T>) => T[];
    type LodashTail = <T>(array: lodash.List<T> | null | undefined) => T[];
    interface LodashTake {
        (n: number): LodashTake1x1;
        <T>(n: lodash.__, array: lodash.List<T> | null | undefined): LodashTake1x2<T>;
        <T>(n: number, array: lodash.List<T> | null | undefined): T[];
    }
    type LodashTake1x1 = <T>(array: lodash.List<T> | null | undefined) => T[];
    type LodashTake1x2<T> = (n: number) => T[];
    interface LodashTakeRight {
        (n: number): LodashTakeRight1x1;
        <T>(n: lodash.__, array: lodash.List<T> | null | undefined): LodashTakeRight1x2<T>;
        <T>(n: number, array: lodash.List<T> | null | undefined): T[];
    }
    type LodashTakeRight1x1 = <T>(array: lodash.List<T> | null | undefined) => T[];
    type LodashTakeRight1x2<T> = (n: number) => T[];
    interface LodashTakeRightWhile {
        <T>(predicate: lodash.ValueIteratee<T>): LodashTakeRightWhile1x1<T>;
        <T>(predicate: lodash.__, array: lodash.List<T> | null | undefined): LodashTakeRightWhile1x2<T>;
        <T>(predicate: lodash.ValueIteratee<T>, array: lodash.List<T> | null | undefined): T[];
    }
    type LodashTakeRightWhile1x1<T> = (array: lodash.List<T> | null | undefined) => T[];
    type LodashTakeRightWhile1x2<T> = (predicate: lodash.ValueIteratee<T>) => T[];
    interface LodashTakeWhile {
        <T>(predicate: lodash.ValueIteratee<T>): LodashTakeWhile1x1<T>;
        <T>(predicate: lodash.__, array: lodash.List<T> | null | undefined): LodashTakeWhile1x2<T>;
        <T>(predicate: lodash.ValueIteratee<T>, array: lodash.List<T> | null | undefined): T[];
    }
    type LodashTakeWhile1x1<T> = (array: lodash.List<T> | null | undefined) => T[];
    type LodashTakeWhile1x2<T> = (predicate: lodash.ValueIteratee<T>) => T[];
    interface LodashTap {
        <T>(interceptor: (value: T) => void): LodashTap1x1<T>;
        <T>(interceptor: lodash.__, value: T): LodashTap1x2<T>;
        <T>(interceptor: (value: T) => void, value: T): T;
    }
    type LodashTap1x1<T> = (value: T) => T;
    type LodashTap1x2<T> = (interceptor: (value: T) => void) => T;
    type LodashTemplate = (string: string) => lodash.TemplateExecutor;
    interface LodashThrottle {
        (wait: number): LodashThrottle1x1;
        <T extends (...args: any) => any>(wait: lodash.__, func: T): LodashThrottle1x2<T>;
        <T extends (...args: any) => any>(wait: number, func: T): lodash.DebouncedFunc<T>;
    }
    type LodashThrottle1x1 = <T extends (...args: any) => any>(func: T) => lodash.DebouncedFunc<T>;
    type LodashThrottle1x2<T extends (...args: any) => any> = (wait: number) => lodash.DebouncedFunc<T>;
    interface LodashThru {
        <T, TResult>(interceptor: (value: T) => TResult): LodashThru1x1<T, TResult>;
        <T>(interceptor: lodash.__, value: T): LodashThru1x2<T>;
        <T, TResult>(interceptor: (value: T) => TResult, value: T): TResult;
    }
    type LodashThru1x1<T, TResult> = (value: T) => TResult;
    type LodashThru1x2<T> = <TResult>(interceptor: (value: T) => TResult) => TResult;
    interface LodashTimes {
        <TResult>(iteratee: (num: number) => TResult): LodashTimes1x1<TResult>;
        (iteratee: lodash.__, n: number): LodashTimes1x2;
        <TResult>(iteratee: (num: number) => TResult, n: number): TResult[];
    }
    type LodashTimes1x1<TResult> = (n: number) => TResult[];
    type LodashTimes1x2 = <TResult>(iteratee: (num: number) => TResult) => TResult[];
    interface LodashToArray {
        <T>(value:  lodash.Dictionary<T> | lodash.NumericDictionary<T> | null | undefined): T[];
        <T>(value: T): Array<T[keyof T]>;
        (): any[];
    }
    type LodashToFinite = (value: any) => number;
    type LodashToInteger = (value: any) => number;
    type LodashToLength = (value: any) => number;
    type LodashToLower = (string: string) => string;
    type LodashToNumber = (value: any) => number;
    type LodashToPath = (value: any) => string[];
    type LodashToPlainObject = (value: any) => any;
    type LodashToSafeInteger = (value: any) => number;
    type LodashToString = (value: any) => string;
    type LodashToUpper = (string: string) => string;
    interface LodashTransform {
        <T, TResult>(iteratee: lodash.MemoVoidIteratorCapped<T, TResult>): LodashTransform1x1<T, TResult>;
        <TResult>(iteratee: lodash.__, accumulator: TResult): LodashTransform1x2<TResult>;
        <T, TResult>(iteratee: lodash.MemoVoidIteratorCapped<T, TResult>, accumulator: TResult): LodashTransform1x3<T, TResult>;
        <T>(iteratee: lodash.__, accumulator: lodash.__, object: ReadonlyArray<T>): LodashTransform1x4<T>;
        <T, TResult>(iteratee: lodash.MemoVoidIteratorCapped<T, TResult>, accumulator: lodash.__, object: ReadonlyArray<T>): LodashTransform1x5<TResult>;
        <T, TResult>(iteratee: lodash.__, accumulator: TResult, object: ReadonlyArray<T>): LodashTransform1x6<T, TResult>;
        <T, TResult>(iteratee: lodash.MemoVoidIteratorCapped<T, TResult>, accumulator: TResult, object: ReadonlyArray<T> | lodash.Dictionary<T>): TResult;
        <T>(iteratee: lodash.__, accumulator: lodash.__, object: lodash.Dictionary<T>): LodashTransform2x4<T>;
        <T, TResult>(iteratee: lodash.MemoVoidIteratorCapped<T, TResult>, accumulator: lodash.__, object: lodash.Dictionary<T>): LodashTransform2x5<TResult>;
        <T, TResult>(iteratee: lodash.__, accumulator: TResult, object: lodash.Dictionary<T>): LodashTransform2x6<T, TResult>;
    }
    interface LodashTransform1x1<T, TResult> {
        (accumulator: TResult): LodashTransform1x3<T, TResult>;
        (accumulator: lodash.__, object: ReadonlyArray<T>): LodashTransform1x5<TResult>;
        (accumulator: TResult, object: ReadonlyArray<T> | lodash.Dictionary<T>): TResult;
        (accumulator: lodash.__, object: lodash.Dictionary<T>): LodashTransform2x5<TResult>;
    }
    interface LodashTransform1x2<TResult> {
        <T>(iteratee: lodash.MemoVoidIteratorCapped<T, TResult>): LodashTransform1x3<T, TResult>;
        <T>(iteratee: lodash.__, object: ReadonlyArray<T>): LodashTransform1x6<T, TResult>;
        <T>(iteratee: lodash.MemoVoidIteratorCapped<T, TResult>, object: ReadonlyArray<T> | lodash.Dictionary<T>): TResult;
        <T>(iteratee: lodash.__, object: lodash.Dictionary<T>): LodashTransform2x6<T, TResult>;
    }
    type LodashTransform1x3<T, TResult> = (object: ReadonlyArray<T> | lodash.Dictionary<T>) => TResult;
    interface LodashTransform1x4<T> {
        <TResult>(iteratee: lodash.MemoVoidIteratorCapped<T, TResult>): LodashTransform1x5<TResult>;
        <TResult>(iteratee: lodash.__, accumulator: TResult): LodashTransform1x6<T, TResult>;
        <TResult>(iteratee: lodash.MemoVoidIteratorCapped<T, TResult>, accumulator: TResult): TResult;
    }
    type LodashTransform1x5<TResult> = (accumulator: TResult) => TResult;
    type LodashTransform1x6<T, TResult> = (iteratee: lodash.MemoVoidIteratorCapped<T, TResult>) => TResult;
    interface LodashTransform2x4<T> {
        <TResult>(iteratee: lodash.MemoVoidIteratorCapped<T, TResult>): LodashTransform2x5<TResult>;
        <TResult>(iteratee: lodash.__, accumulator: TResult): LodashTransform2x6<T, TResult>;
        <TResult>(iteratee: lodash.MemoVoidIteratorCapped<T, TResult>, accumulator: TResult): TResult;
    }
    type LodashTransform2x5<TResult> = (accumulator: TResult) => TResult;
    type LodashTransform2x6<T, TResult> = (iteratee: lodash.MemoVoidIteratorCapped<T, TResult>) => TResult;
    type LodashTrim = (string: string) => string;
    interface LodashTrimChars {
        (chars: string): LodashTrimChars1x1;
        (chars: lodash.__, string: string): LodashTrimChars1x2;
        (chars: string, string: string): string;
    }
    type LodashTrimChars1x1 = (string: string) => string;
    type LodashTrimChars1x2 = (chars: string) => string;
    interface LodashTrimCharsEnd {
        (chars: string): LodashTrimCharsEnd1x1;
        (chars: lodash.__, string: string): LodashTrimCharsEnd1x2;
        (chars: string, string: string): string;
    }
    type LodashTrimCharsEnd1x1 = (string: string) => string;
    type LodashTrimCharsEnd1x2 = (chars: string) => string;
    interface LodashTrimCharsStart {
        (chars: string): LodashTrimCharsStart1x1;
        (chars: lodash.__, string: string): LodashTrimCharsStart1x2;
        (chars: string, string: string): string;
    }
    type LodashTrimCharsStart1x1 = (string: string) => string;
    type LodashTrimCharsStart1x2 = (chars: string) => string;
    type LodashTrimEnd = (string: string) => string;
    type LodashTrimStart = (string: string) => string;
    interface LodashTruncate {
        (options: lodash.TruncateOptions): LodashTruncate1x1;
        (options: lodash.__, string: string): LodashTruncate1x2;
        (options: lodash.TruncateOptions, string: string): string;
    }
    type LodashTruncate1x1 = (string: string) => string;
    type LodashTruncate1x2 = (options: lodash.TruncateOptions) => string;
    type LodashUnapply = (func: (...args: any[]) => any) => (...args: any[]) => any;
    type LodashUnary = <T, TResult>(func: (arg1: T, ...args: any[]) => TResult) => (arg1: T) => TResult;
    type LodashUnescape = (string: string) => string;
    interface LodashUnion {
        <T>(arrays2: lodash.List<T> | null | undefined): LodashUnion1x1<T>;
        <T>(arrays2: lodash.__, arrays: lodash.List<T> | null | undefined): LodashUnion1x2<T>;
        <T>(arrays2: lodash.List<T> | null | undefined, arrays: lodash.List<T> | null | undefined): T[];
    }
    type LodashUnion1x1<T> = (arrays: lodash.List<T> | null | undefined) => T[];
    type LodashUnion1x2<T> = (arrays2: lodash.List<T> | null | undefined) => T[];
    interface LodashUnionBy {
        <T>(iteratee: lodash.ValueIteratee<T>): LodashUnionBy1x1<T>;
        <T>(iteratee: lodash.__, arrays1: lodash.List<T> | null | undefined): LodashUnionBy1x2<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, arrays1: lodash.List<T> | null | undefined): LodashUnionBy1x3<T>;
        <T>(iteratee: lodash.__, arrays1: lodash.__, arrays2: lodash.List<T> | null | undefined): LodashUnionBy1x4<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, arrays1: lodash.__, arrays2: lodash.List<T> | null | undefined): LodashUnionBy1x5<T>;
        <T>(iteratee: lodash.__, arrays1: lodash.List<T> | null | undefined, arrays2: lodash.List<T> | null | undefined): LodashUnionBy1x6<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, arrays1: lodash.List<T> | null | undefined, arrays2: lodash.List<T> | null | undefined): T[];
    }
    interface LodashUnionBy1x1<T> {
        (arrays1: lodash.List<T> | null | undefined): LodashUnionBy1x3<T>;
        (arrays1: lodash.__, arrays2: lodash.List<T> | null | undefined): LodashUnionBy1x5<T>;
        (arrays1: lodash.List<T> | null | undefined, arrays2: lodash.List<T> | null | undefined): T[];
    }
    interface LodashUnionBy1x2<T> {
        (iteratee: lodash.ValueIteratee<T>): LodashUnionBy1x3<T>;
        (iteratee: lodash.__, arrays2: lodash.List<T> | null | undefined): LodashUnionBy1x6<T>;
        (iteratee: lodash.ValueIteratee<T>, arrays2: lodash.List<T> | null | undefined): T[];
    }
    type LodashUnionBy1x3<T> = (arrays2: lodash.List<T> | null | undefined) => T[];
    interface LodashUnionBy1x4<T> {
        (iteratee: lodash.ValueIteratee<T>): LodashUnionBy1x5<T>;
        (iteratee: lodash.__, arrays1: lodash.List<T> | null | undefined): LodashUnionBy1x6<T>;
        (iteratee: lodash.ValueIteratee<T>, arrays1: lodash.List<T> | null | undefined): T[];
    }
    type LodashUnionBy1x5<T> = (arrays1: lodash.List<T> | null | undefined) => T[];
    type LodashUnionBy1x6<T> = (iteratee: lodash.ValueIteratee<T>) => T[];
    interface LodashUnionWith {
        <T>(comparator: lodash.Comparator<T>): LodashUnionWith1x1<T>;
        <T>(comparator: lodash.__, arrays: lodash.List<T> | null | undefined): LodashUnionWith1x2<T>;
        <T>(comparator: lodash.Comparator<T>, arrays: lodash.List<T> | null | undefined): LodashUnionWith1x3<T>;
        <T>(comparator: lodash.__, arrays: lodash.__, arrays2: lodash.List<T> | null | undefined): LodashUnionWith1x4<T>;
        <T>(comparator: lodash.Comparator<T>, arrays: lodash.__, arrays2: lodash.List<T> | null | undefined): LodashUnionWith1x5<T>;
        <T>(comparator: lodash.__, arrays: lodash.List<T> | null | undefined, arrays2: lodash.List<T> | null | undefined): LodashUnionWith1x6<T>;
        <T>(comparator: lodash.Comparator<T>, arrays: lodash.List<T> | null | undefined, arrays2: lodash.List<T> | null | undefined): T[];
    }
    interface LodashUnionWith1x1<T> {
        (arrays: lodash.List<T> | null | undefined): LodashUnionWith1x3<T>;
        (arrays: lodash.__, arrays2: lodash.List<T> | null | undefined): LodashUnionWith1x5<T>;
        (arrays: lodash.List<T> | null | undefined, arrays2: lodash.List<T> | null | undefined): T[];
    }
    interface LodashUnionWith1x2<T> {
        (comparator: lodash.Comparator<T>): LodashUnionWith1x3<T>;
        (comparator: lodash.__, arrays2: lodash.List<T> | null | undefined): LodashUnionWith1x6<T>;
        (comparator: lodash.Comparator<T>, arrays2: lodash.List<T> | null | undefined): T[];
    }
    type LodashUnionWith1x3<T> = (arrays2: lodash.List<T> | null | undefined) => T[];
    interface LodashUnionWith1x4<T> {
        (comparator: lodash.Comparator<T>): LodashUnionWith1x5<T>;
        (comparator: lodash.__, arrays: lodash.List<T> | null | undefined): LodashUnionWith1x6<T>;
        (comparator: lodash.Comparator<T>, arrays: lodash.List<T> | null | undefined): T[];
    }
    type LodashUnionWith1x5<T> = (arrays: lodash.List<T> | null | undefined) => T[];
    type LodashUnionWith1x6<T> = (comparator: lodash.Comparator<T>) => T[];
    type LodashUniq = <T>(array: lodash.List<T> | null | undefined) => T[];
    interface LodashUniqBy {
        <T>(iteratee: lodash.ValueIteratee<T>): LodashUniqBy1x1<T>;
        <T>(iteratee: lodash.__, array: lodash.List<T> | null | undefined): LodashUniqBy1x2<T>;
        <T>(iteratee: lodash.ValueIteratee<T>, array: lodash.List<T> | null | undefined): T[];
    }
    type LodashUniqBy1x1<T> = (array: lodash.List<T> | null | undefined) => T[];
    type LodashUniqBy1x2<T> = (iteratee: lodash.ValueIteratee<T>) => T[];
    type LodashUniqueId = (prefix: string) => string;
    interface LodashUniqWith {
        <T>(comparator: lodash.Comparator<T>): LodashUniqWith1x1<T>;
        <T>(comparator: lodash.__, array: lodash.List<T> | null | undefined): LodashUniqWith1x2<T>;
        <T>(comparator: lodash.Comparator<T>, array: lodash.List<T> | null | undefined): T[];
    }
    type LodashUniqWith1x1<T> = (array: lodash.List<T> | null | undefined) => T[];
    type LodashUniqWith1x2<T> = (comparator: lodash.Comparator<T>) => T[];
    type LodashUnzip = <T>(array: T[][] | lodash.List<lodash.List<T>> | null | undefined) => T[][];
    interface LodashUnzipWith {
        <T, TResult>(iteratee: (...values: T[]) => TResult): LodashUnzipWith1x1<T, TResult>;
        <T>(iteratee: lodash.__, array: lodash.List<lodash.List<T>> | null | undefined): LodashUnzipWith1x2<T>;
        <T, TResult>(iteratee: (...values: T[]) => TResult, array: lodash.List<lodash.List<T>> | null | undefined): TResult[];
    }
    type LodashUnzipWith1x1<T, TResult> = (array: lodash.List<lodash.List<T>> | null | undefined) => TResult[];
    type LodashUnzipWith1x2<T> = <TResult>(iteratee: (...values: T[]) => TResult) => TResult[];
    interface LodashUpdate {
        (path: lodash.PropertyPath): LodashUpdate1x1;
        (path: lodash.__, updater: (value: any) => any): LodashUpdate1x2;
        (path: lodash.PropertyPath, updater: (value: any) => any): LodashUpdate1x3;
        (path: lodash.__, updater: lodash.__, object: object): LodashUpdate1x4;
        (path: lodash.PropertyPath, updater: lodash.__, object: object): LodashUpdate1x5;
        (path: lodash.__, updater: (value: any) => any, object: object): LodashUpdate1x6;
        (path: lodash.PropertyPath, updater: (value: any) => any, object: object): any;
    }
    interface LodashUpdate1x1 {
        (updater: (value: any) => any): LodashUpdate1x3;
        (updater: lodash.__, object: object): LodashUpdate1x5;
        (updater: (value: any) => any, object: object): any;
    }
    interface LodashUpdate1x2 {
        (path: lodash.PropertyPath): LodashUpdate1x3;
        (path: lodash.__, object: object): LodashUpdate1x6;
        (path: lodash.PropertyPath, object: object): any;
    }
    type LodashUpdate1x3 = (object: object) => any;
    interface LodashUpdate1x4 {
        (path: lodash.PropertyPath): LodashUpdate1x5;
        (path: lodash.__, updater: (value: any) => any): LodashUpdate1x6;
        (path: lodash.PropertyPath, updater: (value: any) => any): any;
    }
    type LodashUpdate1x5 = (updater: (value: any) => any) => any;
    type LodashUpdate1x6 = (path: lodash.PropertyPath) => any;
    interface LodashUpdateWith {
        <T extends object>(customizer: lodash.SetWithCustomizer<T>): LodashUpdateWith1x1<T>;
        (customizer: lodash.__, path: lodash.PropertyPath): LodashUpdateWith1x2;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.PropertyPath): LodashUpdateWith1x3<T>;
        (customizer: lodash.__, path: lodash.__, updater: (oldValue: any) => any): LodashUpdateWith1x4;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.__, updater: (oldValue: any) => any): LodashUpdateWith1x5<T>;
        (customizer: lodash.__, path: lodash.PropertyPath, updater: (oldValue: any) => any): LodashUpdateWith1x6;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.PropertyPath, updater: (oldValue: any) => any): LodashUpdateWith1x7<T>;
        <T extends object>(customizer: lodash.__, path: lodash.__, updater: lodash.__, object: T): LodashUpdateWith1x8<T>;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.__, updater: lodash.__, object: T): LodashUpdateWith1x9<T>;
        <T extends object>(customizer: lodash.__, path: lodash.PropertyPath, updater: lodash.__, object: T): LodashUpdateWith1x10<T>;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.PropertyPath, updater: lodash.__, object: T): LodashUpdateWith1x11<T>;
        <T extends object>(customizer: lodash.__, path: lodash.__, updater: (oldValue: any) => any, object: T): LodashUpdateWith1x12<T>;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.__, updater: (oldValue: any) => any, object: T): LodashUpdateWith1x13<T>;
        <T extends object>(customizer: lodash.__, path: lodash.PropertyPath, updater: (oldValue: any) => any, object: T): LodashUpdateWith1x14<T>;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.PropertyPath, updater: (oldValue: any) => any, object: T): T;
    }
    interface LodashUpdateWith1x1<T> {
        (path: lodash.PropertyPath): LodashUpdateWith1x3<T>;
        (path: lodash.__, updater: (oldValue: any) => any): LodashUpdateWith1x5<T>;
        (path: lodash.PropertyPath, updater: (oldValue: any) => any): LodashUpdateWith1x7<T>;
        (path: lodash.__, updater: lodash.__, object: T): LodashUpdateWith1x9<T>;
        (path: lodash.PropertyPath, updater: lodash.__, object: T): LodashUpdateWith1x11<T>;
        (path: lodash.__, updater: (oldValue: any) => any, object: T): LodashUpdateWith1x13<T>;
        (path: lodash.PropertyPath, updater: (oldValue: any) => any, object: T): T;
    }
    interface LodashUpdateWith1x2 {
        <T extends object>(customizer: lodash.SetWithCustomizer<T>): LodashUpdateWith1x3<T>;
        (customizer: lodash.__, updater: (oldValue: any) => any): LodashUpdateWith1x6;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, updater: (oldValue: any) => any): LodashUpdateWith1x7<T>;
        <T extends object>(customizer: lodash.__, updater: lodash.__, object: T): LodashUpdateWith1x10<T>;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, updater: lodash.__, object: T): LodashUpdateWith1x11<T>;
        <T extends object>(customizer: lodash.__, updater: (oldValue: any) => any, object: T): LodashUpdateWith1x14<T>;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, updater: (oldValue: any) => any, object: T): T;
    }
    interface LodashUpdateWith1x3<T> {
        (updater: (oldValue: any) => any): LodashUpdateWith1x7<T>;
        (updater: lodash.__, object: T): LodashUpdateWith1x11<T>;
        (updater: (oldValue: any) => any, object: T): T;
    }
    interface LodashUpdateWith1x4 {
        <T extends object>(customizer: lodash.SetWithCustomizer<T>): LodashUpdateWith1x5<T>;
        (customizer: lodash.__, path: lodash.PropertyPath): LodashUpdateWith1x6;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.PropertyPath): LodashUpdateWith1x7<T>;
        <T extends object>(customizer: lodash.__, path: lodash.__, object: T): LodashUpdateWith1x12<T>;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.__, object: T): LodashUpdateWith1x13<T>;
        <T extends object>(customizer: lodash.__, path: lodash.PropertyPath, object: T): LodashUpdateWith1x14<T>;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, path: lodash.PropertyPath, object: T): T;
    }
    interface LodashUpdateWith1x5<T> {
        (path: lodash.PropertyPath): LodashUpdateWith1x7<T>;
        (path: lodash.__, object: T): LodashUpdateWith1x13<T>;
        (path: lodash.PropertyPath, object: T): T;
    }
    interface LodashUpdateWith1x6 {
        <T extends object>(customizer: lodash.SetWithCustomizer<T>): LodashUpdateWith1x7<T>;
        <T extends object>(customizer: lodash.__, object: T): LodashUpdateWith1x14<T>;
        <T extends object>(customizer: lodash.SetWithCustomizer<T>, object: T): T;
    }
    type LodashUpdateWith1x7<T> = (object: T) => T;
    interface LodashUpdateWith1x8<T> {
        (customizer: lodash.SetWithCustomizer<T>): LodashUpdateWith1x9<T>;
        (customizer: lodash.__, path: lodash.PropertyPath): LodashUpdateWith1x10<T>;
        (customizer: lodash.SetWithCustomizer<T>, path: lodash.PropertyPath): LodashUpdateWith1x11<T>;
        (customizer: lodash.__, path: lodash.__, updater: (oldValue: any) => any): LodashUpdateWith1x12<T>;
        (customizer: lodash.SetWithCustomizer<T>, path: lodash.__, updater: (oldValue: any) => any): LodashUpdateWith1x13<T>;
        (customizer: lodash.__, path: lodash.PropertyPath, updater: (oldValue: any) => any): LodashUpdateWith1x14<T>;
        (customizer: lodash.SetWithCustomizer<T>, path: lodash.PropertyPath, updater: (oldValue: any) => any): T;
    }
    interface LodashUpdateWith1x9<T> {
        (path: lodash.PropertyPath): LodashUpdateWith1x11<T>;
        (path: lodash.__, updater: (oldValue: any) => any): LodashUpdateWith1x13<T>;
        (path: lodash.PropertyPath, updater: (oldValue: any) => any): T;
    }
    interface LodashUpdateWith1x10<T> {
        (customizer: lodash.SetWithCustomizer<T>): LodashUpdateWith1x11<T>;
        (customizer: lodash.__, updater: (oldValue: any) => any): LodashUpdateWith1x14<T>;
        (customizer: lodash.SetWithCustomizer<T>, updater: (oldValue: any) => any): T;
    }
    type LodashUpdateWith1x11<T> = (updater: (oldValue: any) => any) => T;
    interface LodashUpdateWith1x12<T> {
        (customizer: lodash.SetWithCustomizer<T>): LodashUpdateWith1x13<T>;
        (customizer: lodash.__, path: lodash.PropertyPath): LodashUpdateWith1x14<T>;
        (customizer: lodash.SetWithCustomizer<T>, path: lodash.PropertyPath): T;
    }
    type LodashUpdateWith1x13<T> = (path: lodash.PropertyPath) => T;
    type LodashUpdateWith1x14<T> = (customizer: lodash.SetWithCustomizer<T>) => T;
    type LodashUpperCase = (string: string) => string;
    type LodashUpperFirst = (string: string) => string;
    interface LodashValues {
        <T>(object: lodash.Dictionary<T> | lodash.NumericDictionary<T> | lodash.List<T> | null | undefined): T[];
        <T extends object>(object: T | null | undefined): Array<T[keyof T]>;
        (object: any): any[];
    }
    interface LodashValuesIn {
        <T>(object: lodash.Dictionary<T> | lodash.NumericDictionary<T> | lodash.List<T> | null | undefined): T[];
        <T extends object>(object: T | null | undefined): Array<T[keyof T]>;
    }
    interface LodashWithout {
        <T>(values: ReadonlyArray<T>): LodashWithout1x1<T>;
        <T>(values: lodash.__, array: lodash.List<T> | null | undefined): LodashWithout1x2<T>;
        <T>(values: ReadonlyArray<T>, array: lodash.List<T> | null | undefined): T[];
    }
    type LodashWithout1x1<T> = (array: lodash.List<T> | null | undefined) => T[];
    type LodashWithout1x2<T> = (values: ReadonlyArray<T>) => T[];
    type LodashWords = (string: string) => string[];
    interface LodashWrap {
        <T, TArgs, TResult>(wrapper: (value: T, ...args: TArgs[]) => TResult): LodashWrap1x1<T, TArgs, TResult>;
        <T>(wrapper: lodash.__, value: T): LodashWrap1x2<T>;
        <T, TArgs, TResult>(wrapper: (value: T, ...args: TArgs[]) => TResult, value: T): (...args: TArgs[]) => TResult;
    }
    type LodashWrap1x1<T, TArgs, TResult> = (value: T) => (...args: TArgs[]) => TResult;
    type LodashWrap1x2<T> = <TArgs, TResult>(wrapper: (value: T, ...args: TArgs[]) => TResult) => (...args: TArgs[]) => TResult;
    interface LodashZip {
        <T1>(arrays1: lodash.List<T1>): LodashZip1x1<T1>;
        <T2>(arrays1: lodash.__, arrays2: lodash.List<T2>): LodashZip1x2<T2>;
        <T1, T2>(arrays1: lodash.List<T1>, arrays2: lodash.List<T2>): Array<[T1 | undefined, T2 | undefined]>;
    }
    type LodashZip1x1<T1> = <T2>(arrays2: lodash.List<T2>) => Array<[T1 | undefined, T2 | undefined]>;
    type LodashZip1x2<T2> = <T1>(arrays1: lodash.List<T1>) => Array<[T1 | undefined, T2 | undefined]>;
    interface LodashZipAll {
        <T1, T2>(arrays1: [lodash.List<T1>, lodash.List<T2>]): Array<[T1 | undefined, T2 | undefined]>;
        <T1, T2, T3>(arrays1: [lodash.List<T1>, lodash.List<T2>, lodash.List<T3>]): Array<[T1 | undefined, T2 | undefined, T3 | undefined]>;
        <T1, T2, T3, T4>(arrays1: [lodash.List<T1>, lodash.List<T2>, lodash.List<T3>, lodash.List<T4>]): Array<[T1 | undefined, T2 | undefined, T3 | undefined, T4 | undefined]>;
        <T1, T2, T3, T4, T5>(arrays1: [lodash.List<T1>, lodash.List<T2>, lodash.List<T3>, lodash.List<T4>, lodash.List<T5>]): Array<[T1 | undefined, T2 | undefined, T3 | undefined, T4 | undefined, T5 | undefined]>;
        <T>(arrays: ReadonlyArray<lodash.List<T> | null | undefined>): Array<Array<T | undefined>>;
    }
    interface LodashZipObject {
        (props: lodash.List<lodash.PropertyName>): LodashZipObject1x1;
        <T>(props: lodash.__, values: lodash.List<T>): LodashZipObject1x2<T>;
        <T>(props: lodash.List<lodash.PropertyName>, values: lodash.List<T>): lodash.Dictionary<T>;
    }
    type LodashZipObject1x1 = <T>(values: lodash.List<T>) => lodash.Dictionary<T>;
    type LodashZipObject1x2<T> = (props: lodash.List<lodash.PropertyName>) => lodash.Dictionary<T>;
    interface LodashZipObjectDeep {
        (paths: lodash.List<lodash.PropertyPath>): LodashZipObjectDeep1x1;
        (paths: lodash.__, values: lodash.List<any>): LodashZipObjectDeep1x2;
        (paths: lodash.List<lodash.PropertyPath>, values: lodash.List<any>): object;
    }
    type LodashZipObjectDeep1x1 = (values: lodash.List<any>) => object;
    type LodashZipObjectDeep1x2 = (paths: lodash.List<lodash.PropertyPath>) => object;
    interface LodashZipWith {
        <T1, T2, TResult>(iteratee: (value1: T1, value2: T2) => TResult): LodashZipWith1x1<T1, T2, TResult>;
        <T1>(iteratee: lodash.__, arrays1: lodash.List<T1>): LodashZipWith1x2<T1>;
        <T1, T2, TResult>(iteratee: (value1: T1, value2: T2) => TResult, arrays1: lodash.List<T1>): LodashZipWith1x3<T2, TResult>;
        <T2>(iteratee: lodash.__, arrays1: lodash.__, arrays2: lodash.List<T2>): LodashZipWith1x4<T2>;
        <T1, T2, TResult>(iteratee: (value1: T1, value2: T2) => TResult, arrays1: lodash.__, arrays2: lodash.List<T2>): LodashZipWith1x5<T1, TResult>;
        <T1, T2>(iteratee: lodash.__, arrays1: lodash.List<T1>, arrays2: lodash.List<T2>): LodashZipWith1x6<T1, T2>;
        <T1, T2, TResult>(iteratee: (value1: T1, value2: T2) => TResult, arrays1: lodash.List<T1>, arrays2: lodash.List<T2>): TResult[];
    }
    interface LodashZipWith1x1<T1, T2, TResult> {
        (arrays1: lodash.List<T1>): LodashZipWith1x3<T2, TResult>;
        (arrays1: lodash.__, arrays2: lodash.List<T2>): LodashZipWith1x5<T1, TResult>;
        (arrays1: lodash.List<T1>, arrays2: lodash.List<T2>): TResult[];
    }
    interface LodashZipWith1x2<T1> {
        <T2, TResult>(iteratee: (value1: T1, value2: T2) => TResult): LodashZipWith1x3<T2, TResult>;
        <T2>(iteratee: lodash.__, arrays2: lodash.List<T2>): LodashZipWith1x6<T1, T2>;
        <T2, TResult>(iteratee: (value1: T1, value2: T2) => TResult, arrays2: lodash.List<T2>): TResult[];
    }
    type LodashZipWith1x3<T2, TResult> = (arrays2: lodash.List<T2>) => TResult[];
    interface LodashZipWith1x4<T2> {
        <T1, TResult>(iteratee: (value1: T1, value2: T2) => TResult): LodashZipWith1x5<T1, TResult>;
        <T1>(iteratee: lodash.__, arrays1: lodash.List<T1>): LodashZipWith1x6<T1, T2>;
        <T1, TResult>(iteratee: (value1: T1, value2: T2) => TResult, arrays1: lodash.List<T1>): TResult[];
    }
    type LodashZipWith1x5<T1, TResult> = (arrays1: lodash.List<T1>) => TResult[];
    type LodashZipWith1x6<T1, T2> = <TResult>(iteratee: (value1: T1, value2: T2) => TResult) => TResult[];

    interface LoDashFp {
        add: LodashAdd;
        after: LodashAfter;
        all: LodashEvery;
        allPass: LodashOverEvery;
        always: LodashConstant;
        any: LodashSome;
        anyPass: LodashOverSome;
        apply: LodashApply;
        ary: LodashAry;
        assign: LodashAssign;
        assignAll: LodashAssignAll;
        assignAllWith: LodashAssignAllWith;
        assignIn: LodashAssignIn;
        assignInAll: LodashAssignInAll;
        assignInAllWith: LodashAssignInAllWith;
        assignInWith: LodashAssignInWith;
        assignWith: LodashAssignWith;
        assoc: LodashSet;
        assocPath: LodashSet;
        at: LodashAt;
        attempt: LodashAttempt;
        before: LodashBefore;
        bind: LodashBind;
        bindAll: LodashBindAll;
        bindKey: LodashBindKey;
        camelCase: LodashCamelCase;
        capitalize: LodashCapitalize;
        castArray: LodashCastArray;
        ceil: LodashCeil;
        chunk: LodashChunk;
        clamp: LodashClamp;
        clone: LodashClone;
        cloneDeep: LodashCloneDeep;
        cloneDeepWith: LodashCloneDeepWith;
        cloneWith: LodashCloneWith;
        compact: LodashCompact;
        complement: LodashNegate;
        compose: LodashFlowRight;
        concat: LodashConcat;
        cond: LodashCond;
        conforms: LodashConformsTo;
        conformsTo: LodashConformsTo;
        constant: LodashConstant;
        contains: LodashContains;
        countBy: LodashCountBy;
        create: LodashCreate;
        curry: LodashCurry;
        curryN: LodashCurryN;
        curryRight: LodashCurryRight;
        curryRightN: LodashCurryRightN;
        debounce: LodashDebounce;
        deburr: LodashDeburr;
        defaults: LodashDefaults;
        defaultsAll: LodashDefaultsAll;
        defaultsDeep: LodashDefaultsDeep;
        defaultsDeepAll: LodashDefaultsDeepAll;
        defaultTo: LodashDefaultTo;
        defer: LodashDefer;
        delay: LodashDelay;
        difference: LodashDifference;
        differenceBy: LodashDifferenceBy;
        differenceWith: LodashDifferenceWith;
        dissoc: LodashUnset;
        dissocPath: LodashUnset;
        divide: LodashDivide;
        drop: LodashDrop;
        dropLast: LodashDropRight;
        dropLastWhile: LodashDropRightWhile;
        dropRight: LodashDropRight;
        dropRightWhile: LodashDropRightWhile;
        dropWhile: LodashDropWhile;
        each: LodashForEach;
        eachRight: LodashForEachRight;
        endsWith: LodashEndsWith;
        entries: LodashToPairs;
        entriesIn: LodashToPairsIn;
        eq: LodashEq;
        equals: LodashIsEqual;
        escape: LodashEscape;
        escapeRegExp: LodashEscapeRegExp;
        every: LodashEvery;
        extend: LodashExtend;
        extendAll: LodashExtendAll;
        extendAllWith: LodashExtendAllWith;
        extendWith: LodashExtendWith;
        F: LodashStubFalse;
        fill: LodashFill;
        filter: LodashFilter;
        find: LodashFind;
        findFrom: LodashFindFrom;
        findIndex: LodashFindIndex;
        findIndexFrom: LodashFindIndexFrom;
        findKey: LodashFindKey;
        findLast: LodashFindLast;
        findLastFrom: LodashFindLastFrom;
        findLastIndex: LodashFindLastIndex;
        findLastIndexFrom: LodashFindLastIndexFrom;
        findLastKey: LodashFindLastKey;
        first: LodashHead;
        flatMap: LodashFlatMap;
        flatMapDeep: LodashFlatMapDeep;
        flatMapDepth: LodashFlatMapDepth;
        flatten: LodashFlatten;
        flattenDeep: LodashFlattenDeep;
        flattenDepth: LodashFlattenDepth;
        flip: LodashFlip;
        floor: LodashFloor;
        flow: LodashFlow;
        flowRight: LodashFlowRight;
        forEach: LodashForEach;
        forEachRight: LodashForEachRight;
        forIn: LodashForIn;
        forInRight: LodashForInRight;
        forOwn: LodashForOwn;
        forOwnRight: LodashForOwnRight;
        fromPairs: LodashFromPairs;
        functions: LodashFunctions;
        functionsIn: LodashFunctionsIn;
        get: LodashGet;
        getOr: LodashGetOr;
        groupBy: LodashGroupBy;
        gt: LodashGt;
        gte: LodashGte;
        has: LodashHas;
        hasIn: LodashHasIn;
        head: LodashHead;
        identical: LodashEq;
        identity: LodashIdentity;
        includes: LodashIncludes;
        includesFrom: LodashIncludesFrom;
        indexBy: LodashKeyBy;
        indexOf: LodashIndexOf;
        indexOfFrom: LodashIndexOfFrom;
        init: LodashInitial;
        initial: LodashInitial;
        inRange: LodashInRange;
        intersection: LodashIntersection;
        intersectionBy: LodashIntersectionBy;
        intersectionWith: LodashIntersectionWith;
        invert: LodashInvert;
        invertBy: LodashInvertBy;
        invertObj: LodashInvert;
        invoke: LodashInvoke;
        invokeArgs: LodashInvokeArgs;
        invokeArgsMap: LodashInvokeArgsMap;
        invokeMap: LodashInvokeMap;
        isArguments: LodashIsArguments;
        isArray: LodashIsArray;
        isArrayBuffer: LodashIsArrayBuffer;
        isArrayLike: LodashIsArrayLike;
        isArrayLikeObject: LodashIsArrayLikeObject;
        isBoolean: LodashIsBoolean;
        isBuffer: LodashIsBuffer;
        isDate: LodashIsDate;
        isElement: LodashIsElement;
        isEmpty: LodashIsEmpty;
        isEqual: LodashIsEqual;
        isEqualWith: LodashIsEqualWith;
        isError: LodashIsError;
        isFinite: LodashIsFinite;
        isFunction: LodashIsFunction;
        isInteger: LodashIsInteger;
        isLength: LodashIsLength;
        isMap: LodashIsMap;
        isMatch: LodashIsMatch;
        isMatchWith: LodashIsMatchWith;
        isNaN: LodashIsNaN;
        isNative: LodashIsNative;
        isNil: LodashIsNil;
        isNull: LodashIsNull;
        isNumber: LodashIsNumber;
        isObject: LodashIsObject;
        isObjectLike: LodashIsObjectLike;
        isPlainObject: LodashIsPlainObject;
        isRegExp: LodashIsRegExp;
        isSafeInteger: LodashIsSafeInteger;
        isSet: LodashIsSet;
        isString: LodashIsString;
        isSymbol: LodashIsSymbol;
        isTypedArray: LodashIsTypedArray;
        isUndefined: LodashIsUndefined;
        isWeakMap: LodashIsWeakMap;
        isWeakSet: LodashIsWeakSet;
        iteratee: LodashIteratee;
        join: LodashJoin;
        juxt: LodashOver;
        kebabCase: LodashKebabCase;
        keyBy: LodashKeyBy;
        keys: LodashKeys;
        keysIn: LodashKeysIn;
        last: LodashLast;
        lastIndexOf: LodashLastIndexOf;
        lastIndexOfFrom: LodashLastIndexOfFrom;
        lowerCase: LodashLowerCase;
        lowerFirst: LodashLowerFirst;
        lt: LodashLt;
        lte: LodashLte;
        map: LodashMap;
        mapKeys: LodashMapKeys;
        mapValues: LodashMapValues;
        matches: LodashIsMatch;
        matchesProperty: LodashMatchesProperty;
        max: LodashMax;
        maxBy: LodashMaxBy;
        mean: LodashMean;
        meanBy: LodashMeanBy;
        memoize: LodashMemoize;
        merge: LodashMerge;
        mergeAll: LodashMergeAll;
        mergeAllWith: LodashMergeAllWith;
        mergeWith: LodashMergeWith;
        method: LodashMethod;
        methodOf: LodashMethodOf;
        min: LodashMin;
        minBy: LodashMinBy;
        multiply: LodashMultiply;
        nAry: LodashAry;
        negate: LodashNegate;
        noConflict: LodashNoConflict;
        noop: LodashNoop;
        now: LodashNow;
        nth: LodashNth;
        nthArg: LodashNthArg;
        omit: LodashOmit;
        omitAll: LodashOmit;
        omitBy: LodashOmitBy;
        once: LodashOnce;
        orderBy: LodashOrderBy;
        over: LodashOver;
        overArgs: LodashOverArgs;
        overEvery: LodashOverEvery;
        overSome: LodashOverSome;
        pad: LodashPad;
        padChars: LodashPadChars;
        padCharsEnd: LodashPadCharsEnd;
        padCharsStart: LodashPadCharsStart;
        padEnd: LodashPadEnd;
        padStart: LodashPadStart;
        parseInt: LodashParseInt;
        partial: LodashPartial;
        partialRight: LodashPartialRight;
        partition: LodashPartition;
        path: LodashPath;
        pathEq: LodashMatchesProperty;
        pathOr: LodashPathOr;
        paths: LodashAt;
        pick: LodashPick;
        pickAll: LodashPick;
        pickBy: LodashPickBy;
        pipe: LodashFlow;
        pluck: LodashMap;
        prop: LodashProp;
        propEq: LodashMatchesProperty;
        property: LodashProperty;
        propertyOf: LodashPropertyOf;
        propOr: LodashPropOr;
        props: LodashAt;
        pull: LodashPull;
        pullAll: LodashPullAll;
        pullAllBy: LodashPullAllBy;
        pullAllWith: LodashPullAllWith;
        pullAt: LodashPullAt;
        random: LodashRandom;
        range: LodashRange;
        rangeRight: LodashRangeRight;
        rangeStep: LodashRangeStep;
        rangeStepRight: LodashRangeStepRight;
        rearg: LodashRearg;
        reduce: LodashReduce;
        reduceRight: LodashReduceRight;
        reject: LodashReject;
        remove: LodashRemove;
        repeat: LodashRepeat;
        replace: LodashReplace;
        rest: LodashRest;
        restFrom: LodashRestFrom;
        result: LodashResult;
        reverse: LodashReverse;
        round: LodashRound;
        runInContext: LodashRunInContext;
        sample: LodashSample;
        sampleSize: LodashSampleSize;
        set: LodashSet;
        setWith: LodashSetWith;
        shuffle: LodashShuffle;
        size: LodashSize;
        slice: LodashSlice;
        snakeCase: LodashSnakeCase;
        some: LodashSome;
        sortBy: LodashSortBy;
        sortedIndex: LodashSortedIndex;
        sortedIndexBy: LodashSortedIndexBy;
        sortedIndexOf: LodashSortedIndexOf;
        sortedLastIndex: LodashSortedLastIndex;
        sortedLastIndexBy: LodashSortedLastIndexBy;
        sortedLastIndexOf: LodashSortedLastIndexOf;
        sortedUniq: LodashSortedUniq;
        sortedUniqBy: LodashSortedUniqBy;
        split: LodashSplit;
        spread: LodashSpread;
        spreadFrom: LodashSpreadFrom;
        startCase: LodashStartCase;
        startsWith: LodashStartsWith;
        stubArray: LodashStubArray;
        stubFalse: LodashStubFalse;
        stubObject: LodashStubObject;
        stubString: LodashStubString;
        stubTrue: LodashStubTrue;
        subtract: LodashSubtract;
        sum: LodashSum;
        sumBy: LodashSumBy;
        symmetricDifference: LodashXor;
        symmetricDifferenceBy: LodashXorBy;
        symmetricDifferenceWith: LodashXorWith;
        T: LodashStubTrue;
        tail: LodashTail;
        take: LodashTake;
        takeLast: LodashTakeRight;
        takeLastWhile: LodashTakeRightWhile;
        takeRight: LodashTakeRight;
        takeRightWhile: LodashTakeRightWhile;
        takeWhile: LodashTakeWhile;
        tap: LodashTap;
        template: LodashTemplate;
        throttle: LodashThrottle;
        thru: LodashThru;
        times: LodashTimes;
        toArray: LodashToArray;
        toFinite: LodashToFinite;
        toInteger: LodashToInteger;
        toLength: LodashToLength;
        toLower: LodashToLower;
        toNumber: LodashToNumber;
        toPairs: LodashToPairs;
        toPairsIn: LodashToPairsIn;
        toPath: LodashToPath;
        toPlainObject: LodashToPlainObject;
        toSafeInteger: LodashToSafeInteger;
        toString: LodashToString;
        toUpper: LodashToUpper;
        transform: LodashTransform;
        trim: LodashTrim;
        trimChars: LodashTrimChars;
        trimCharsEnd: LodashTrimCharsEnd;
        trimCharsStart: LodashTrimCharsStart;
        trimEnd: LodashTrimEnd;
        trimStart: LodashTrimStart;
        truncate: LodashTruncate;
        unapply: LodashUnapply;
        unary: LodashUnary;
        unescape: LodashUnescape;
        union: LodashUnion;
        unionBy: LodashUnionBy;
        unionWith: LodashUnionWith;
        uniq: LodashUniq;
        uniqBy: LodashUniqBy;
        uniqueId: LodashUniqueId;
        uniqWith: LodashUniqWith;
        unnest: LodashFlatten;
        unset: LodashUnset;
        unzip: LodashUnzip;
        unzipWith: LodashUnzipWith;
        update: LodashUpdate;
        updateWith: LodashUpdateWith;
        upperCase: LodashUpperCase;
        upperFirst: LodashUpperFirst;
        useWith: LodashOverArgs;
        values: LodashValues;
        valuesIn: LodashValuesIn;
        where: LodashConformsTo;
        whereEq: LodashIsMatch;
        without: LodashWithout;
        words: LodashWords;
        wrap: LodashWrap;
        xor: LodashXor;
        xorBy: LodashXorBy;
        xorWith: LodashXorWith;
        zip: LodashZip;
        zipAll: LodashZipAll;
        zipObj: LodashZipObject;
        zipObject: LodashZipObject;
        zipObjectDeep: LodashZipObjectDeep;
        zipWith: LodashZipWith;
        __: lodash.__;
        placeholder: lodash.__;
    }
}
