{"name": "@cypress/angular", "version": "0.0.0-development", "description": "Test Angular Components with Cypress", "main": "dist/index.js", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "rollup -c rollup.config.mjs", "postbuild": "node ../../scripts/sync-exported-npm-with-cli.js", "check-ts": "tsc --noEmit", "dev": "rollup -c rollup.config.mjs -w", "lint": "eslint --ext .js,.ts,.json, ."}, "dependencies": {}, "devDependencies": {"@angular/common": "^17.2.0", "@angular/core": "^17.2.0", "@angular/platform-browser-dynamic": "^17.2.0", "@cypress/mount-utils": "0.0.0-development", "rollup": "^4.24.4", "typescript": "~5.4.5", "zone.js": "~0.14.6"}, "peerDependencies": {"@angular/common": ">=17.2", "@angular/core": ">=17.2", "@angular/platform-browser-dynamic": ">=17.2", "rxjs": ">=7.5.0", "zone.js": ">=0.13.0"}, "files": ["dist"], "types": "dist/index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/cypress-io/cypress.git"}, "homepage": "https://github.com/cypress-io/cypress/blob/develop/npm/angular/#readme", "bugs": "https://github.com/cypress-io/cypress/issues/new?assignees=&labels=npm%3A%20%40cypress%2Fangular&template=1-bug-report.md&title=", "keywords": ["angular", "cypress", "cypress-io", "test", "testing"], "contributors": [{"name": "<PERSON>", "social": "@atofstryker"}, {"name": "<PERSON>", "social": "@jordanpowell88"}, {"name": "<PERSON>", "social": "@ZachJW34"}], "module": "dist/index.js", "publishConfig": {"access": "public"}, "nx": {"targets": {"build": {"outputs": ["{workspaceRoot}/cli/angular"]}}}, "standard": {"globals": ["Cypress", "cy", "expect"]}}