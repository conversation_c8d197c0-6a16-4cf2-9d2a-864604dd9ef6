# @cypress/angular

Mount Angular components in the open source [Cypress.io](https://www.cypress.io/) test runner

> **Note:** This package is bundled with the `cypress` package and should not need to be installed separately. See the [Angular Component Testing Docs](https://docs.cypress.io/guides/component-testing/angular/overview) for mounting Angular components. Installing and importing `mount` from `@cypress/angular` should only be done for advanced use-cases.

## Requirements

- Angular 17.2.0+ (Cypress 13 and under supports Angular 13 - 16)

## Development

Run `yarn build` to compile and sync packages to the `cypress` cli package.

## [Changelog](./CHANGELOG.md)
