!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n(e.blobUtil={})}(this,function(e){"use strict";function n(e,n){return new Promise(function(r,t){var o=new Image;n&&(o.crossOrigin=n),o.onload=function(){r(o)},o.onerror=t,o.src=e})}function r(e){var n=document.createElement("canvas");return n.width=e.width,n.height=e.height,n.getContext("2d").drawImage(e,0,0,e.width,e.height,0,0,e.width,e.height),n}function t(e,n){e=e||[],"string"==typeof(n=n||{})&&(n={type:n});try{return new Blob(e,n)}catch(i){if("TypeError"!==i.name)throw i;for(var r="undefined"!=typeof BlobBuilder?BlobBuilder:"undefined"!=typeof MSBlobBuilder?MSBlobBuilder:"undefined"!=typeof MozBlobBuilder?MozBlobBuilder:WebKitBlobBuilder,t=new r,o=0;o<e.length;o+=1)t.append(e[o]);return t.getBlob(n.type)}}function o(e){return("undefined"!=typeof URL?URL:webkitURL).createObjectURL(e)}function i(e){return("undefined"!=typeof URL?URL:webkitURL).revokeObjectURL(e)}function u(e){return new Promise(function(n,r){var t=new FileReader,o="function"==typeof t.readAsBinaryString;t.onloadend=function(){var e=t.result||"";if(o)return n(e);n(h(e))},t.onerror=r,o?t.readAsBinaryString(e):t.readAsArrayBuffer(e)})}function a(e,n){var r=[p(atob(e))];return n?t(r,{type:n}):t(r)}function f(e,n){return a(btoa(e),n)}function c(e){return u(e).then(btoa)}function d(e){var n=e.match(/data:([^;]+)/)[1],r=e.replace(/^[^,]+,/,"");return t([p(atob(r))],{type:n})}function b(e){return c(e).then(function(n){return"data:"+e.type+";base64,"+n})}function l(e,t,o,i){return t=t||"image/png",n(e,o).then(r).then(function(e){return e.toDataURL(t,i)})}function B(e,n,r){return"function"==typeof e.toBlob?new Promise(function(t){e.toBlob(t,n,r)}):Promise.resolve(d(e.toDataURL(n,r)))}function y(e,t,o,i){return t=t||"image/png",n(e,o).then(r).then(function(e){return B(e,t,i)})}function g(e,n){return t([e],n)}function s(e){return new Promise(function(n,r){var t=new FileReader;t.onloadend=function(){var e=t.result||new ArrayBuffer(0);n(e)},t.onerror=r,t.readAsArrayBuffer(e)})}function h(e){for(var n="",r=new Uint8Array(e),t=r.byteLength,o=-1;++o<t;)n+=String.fromCharCode(r[o]);return n}function p(e){for(var n=e.length,r=new ArrayBuffer(n),t=new Uint8Array(r),o=-1;++o<n;)t[o]=e.charCodeAt(o);return r}e.createBlob=t,e.createObjectURL=o,e.revokeObjectURL=i,e.blobToBinaryString=u,e.base64StringToBlob=a,e.binaryStringToBlob=f,e.blobToBase64String=c,e.dataURLToBlob=d,e.blobToDataURL=b,e.imgSrcToDataURL=l,e.canvasToBlob=B,e.imgSrcToBlob=y,e.arrayBufferToBlob=g,e.blobToArrayBuffer=s,e.arrayBufferToBinaryString=h,e.binaryStringToArrayBuffer=p,Object.defineProperty(e,"__esModule",{value:!0})});
