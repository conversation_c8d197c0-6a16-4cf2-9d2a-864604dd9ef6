/*! check-more-types - v2.24.0
 homepage: https://github.com/kensho/check-more-types
 Copyright @ 2014 Kensho license: MIT */

!function(a,b){"object"==typeof exports&&"object"==typeof module?module.exports=b():"function"==typeof define&&define.amd?define([],b):"object"==typeof exports?exports.check=b():a.check=b()}(this,function(){return function(a){function b(d){if(c[d])return c[d].exports;var e=c[d]={exports:{},id:d,loaded:!1};return a[d].call(e.exports,e,e.exports,b),e.loaded=!0,e.exports}var c={};return b.m=a,b.c=c,b.p="",b(0)}([function(a,b,c){"use strict";function d(a){return m.object(a)&&p(a)}function e(a){Object.keys(a).forEach(function(b){m.mixin(a[b],b)})}if("function"!=typeof Function.prototype.bind)throw new Error("Missing Function.prototype.bind, please load es5-shim first");var f=c(1),g=c(3),h=c(5),i=c(6),j=c(7),k=c(8),l=c(9),m={maybe:{},verify:{},not:{},every:i.every,map:i.map};if(!m.defend){var n=function(a,b,c){m.verify.fn(a,"expected a function"),m.verify.array(b,"expected list of predicates"),m.verify.defined(c,"missing args");var d=0,e=0,g=b.length;for(d=0;d<g;d+=1){var h=b[d];if(m.fn(h)){if(!h(c[e])){
var i="Argument "+(e+1)+": "+c[e]+" does not pass predicate";throw f.unemptyString(b[d+1])&&(i+=": "+b[d+1]),new Error(i)}e+=1}}return a.apply(null,c)};m.defend=function(a){var b=Array.prototype.slice.call(arguments,1);return function(){return n(a,b,arguments)}}}m.mixin||(m.mixin=function(a,b){function d(a,b,c){if(!f.object(a))throw new Error("missing object "+a);if(!f.unemptyString(b))throw new Error("missing name");if(!f.fn(c))throw new Error("missing function");a[b]||(a[b]=c)}function e(a){return function(){return!(m.defined(arguments[0])&&!m.nulled(arguments[0]))||a.apply(null,arguments)}}if(f.string(a)&&f.fn(b)){var g=a;a=b,b=g}if(!f.fn(a))throw new Error("expected predicate function for name "+b);if(f.unemptyString(b)||(b=a.name),!f.unemptyString(b))throw new Error("predicate function missing name\n"+a.toString());var h=c(4);d(m,b,a),d(m.maybe,b,e(a)),d(m.not,b,i.notModifier(a)),d(m.verify,b,h(a,b+" failed"))}),m.then||(m.then=function(a,b){return function(){var c="function"==typeof a?a.apply(null,arguments):a;
if(c)return b.apply(null,arguments)}});var o={then:f.fn};o["catch"]=f.fn;var p=l.schema.bind(null,o),q={array:Array.isArray,promise:d};[f,g,q,j,k,h,i,l].forEach(e),m.VERSION="2.24.0",a.exports=m},function(a,b,c){"use strict";function d(a){return"function"==typeof a}function e(a){return"string"==typeof a}function f(a){return e(a)&&Boolean(a)}function g(a){return e(a)&&a.toUpperCase()===a}function h(a){return e(a)&&a.toLowerCase()===a}function i(a){return"object"==typeof a&&!I(a)&&!n(a)&&!q(a)}function j(a){return i(a)&&0===Object.keys(a).length}function k(a){return"number"==typeof a&&!isNaN(a)&&a!==1/0&&a!==-(1/0)}function l(a){return k(a)&&a%1===0}function m(a){return k(a)&&a%1!==0}function n(a){return null===a}function o(a){return k(a)&&a>0}function p(a){return k(a)&&a<0}function q(a){return a instanceof Date}function r(a){return a instanceof RegExp}function s(a){return a instanceof Error}function t(a,b){return a instanceof b}function u(a,b){return"number"==typeof a&&"number"!=typeof b?u(b,a):(I(a)||e(a))&&a.length===b;
}function v(a){return"undefined"!=typeof a}function w(a){return q(a)&&k(Number(a))}function x(a){var b=typeof a;return"number"===b||"boolean"===b||"string"===b||"symbol"===b}function y(a){return"number"==typeof a&&0===a}function z(a,b){return a===b}function A(a){return 0===a||1===a}function B(a){return"boolean"==typeof a}function C(a,b){if(2!==arguments.length)throw new Error("Expected two arguments to check.has, got only "+arguments.length);return Boolean(a&&b&&"string"==typeof b&&"undefined"!=typeof a[b])}function D(a){return""===a}function E(a){var b="string"==typeof a||Array.isArray(a);return b?!a.length:a instanceof Object&&!Object.keys(a).length}function F(a){var b="string"==typeof a||Array.isArray(a);return b?a.length:!(a instanceof Object)||Object.keys(a).length}function G(a,b){return a===b}var H=c(2).curry2,I=Array.isArray;a.exports={bit:A,bool:B,date:q,defined:v,empty:E,emptyObject:j,emptyString:D,equal:H(G),error:s,floatNumber:m,fn:d,has:C,instance:t,intNumber:l,isArray:I,length:H(u),
negative:p,negativeNumber:p,nulled:n,number:k,object:i,positive:o,positiveNumber:o,primitive:x,regexp:r,same:z,string:e,unempty:F,unemptyString:f,upperCase:g,lowerCase:h,validDate:w,zero:y}},function(a,b){"use strict";function c(a,b){return function(c){if(b&&arguments.length>2)throw new Error("Curry2 function "+a.name+" called with too many arguments "+arguments.length);return 2===arguments.length?a(arguments[0],arguments[1]):function(b){return a(c,b)}}}a.exports={curry2:c}},function(a,b,c){"use strict";function d(a){return a>=0}function e(a,b){return r.string(a)&&r.string(b)&&0===b.indexOf(a)}function f(a){return a%2===0}function g(a){return a%2===1}function h(a,b){if(Array.isArray(a))return a.indexOf(b)!==-1;if("string"==typeof a){if("string"!=typeof b)throw new Error("Contains in string should search for string also "+b);return a.indexOf(b)!==-1}return!1}function i(a,b){return typeof b===a}function j(a,b){return r.defined(a)&&r.has(a,"length")&&b>=0&&b<a.length}function k(a,b){return typeof a==typeof b&&a&&b&&a.length===b.length;
}function l(a){if(!Array.isArray(a))return!1;if(!a.length)return!0;var b=a[0];return a.every(function(a){return a===b})}function m(a,b){if(!Array.isArray(a))throw new Error("expected an array");return a.indexOf(b)!==-1}function n(a){return r.number(a)&&a>=0&&a<=1}function o(a){return r.string(a)&&u.test(a)}function p(a,b){s(r.fn(a),"expected function that raises");try{a()}catch(c){return"undefined"==typeof b||"function"==typeof b&&b(c)}return!1}function q(a,b){s(r.unemptyString(a),"missing expected extension",a),s(r.unemptyString(b),"missing filename",b);var c=new RegExp("."+a+"$");return c.test(b)}var r=c(1),s=c(4),t=c(2).curry2,u=/^#(?:[0-9a-fA-F]{3}){1,2}$/,v=t(q),w=v("jpg");a.exports={allSame:l,contains:h,even:f,ext:v,extension:v,found:d,hexRgb:o,index:j,isCss:v("css"),isJpg:w,isJpeg:w,isJs:v("js"),isJson:v("json"),odd:g,oneOf:t(m,!0),raises:p,"throws":p,sameLength:k,startsWith:e,type:t(i),unit:n}},function(a,b,c){"use strict";function d(a,b){return function(){var c;if(a.apply(null,arguments)===!1)throw c=arguments[arguments.length-1],
new Error(e.unemptyString(c)?c:b)}}var e=c(1);a.exports=d},function(a,b,c){"use strict";function d(a){return j.isArray(a)&&a.length>0}function e(a,b){return j.isArray(b)&&b.every(a)}function f(a,b){if(!j.isArray(b))throw new Error("expected array to find bad items");return b.filter(k.notModifier(a))}function g(a,b){var c=j.isArray(a)&&a.every(j.string);return c&&j.bool(b)&&b?a.every(j.lowerCase):c}function h(a,b){return j.isArray(a)&&a.every(function(a){return g(a,b)})}function i(a){return j.isArray(a)&&a.every(j.number)}var j=c(1),k=c(6),l=c(4);l(j.fn(j.isArray),"missing low level isArray"),a.exports={arrayOf:e,arrayOfArraysOfStrings:h,arrayOfStrings:g,strings:g,badItems:f,unemptyArray:d,numbers:i}},function(a,b,c){"use strict";function d(){var a=Array.prototype.slice.call(arguments,0);if(!a.length)throw new Error("empty list of arguments to or");return function(){var b=Array.prototype.slice.call(arguments,0);return a.some(function(a){try{return i.fn(a)?a.apply(null,b):Boolean(a)}catch(c){
return!1}})}}function e(){var a=Array.prototype.slice.call(arguments,0);if(!a.length)throw new Error("empty list of arguments to or");return function(){var b=Array.prototype.slice.call(arguments,0);return a.every(function(a){return i.fn(a)?a.apply(null,b):Boolean(a)})}}function f(a){return function(){return!a.apply(null,arguments)}}function g(a){var b,c;for(b in a)if(a.hasOwnProperty(b)){if(c=a[b],i.object(c)&&g(c)===!1)return!1;if(c===!1)return!1}return!0}function h(a,b){var c,d,e={};for(c in b)b.hasOwnProperty(c)&&(d=b[c],i.fn(d)?e[c]=d(a[c]):i.object(d)&&(e[c]=h(a[c],d)));return e}var i=c(1);a.exports={or:d,and:e,notModifier:f,every:g,map:h}},function(a,b,c){"use strict";function d(a){return h.unemptyString(a)&&/^\d+\.\d+\.\d+$/.test(a)}function e(a){return h.unemptyString(a)&&/^git@/.test(a)}function f(a){return h.string(a)&&40===a.length&&i.test(a)}function g(a){return h.string(a)&&7===a.length&&j.test(a)}var h=c(1),i=/^[0-9a-f]{40}$/,j=/^[0-9a-f]{7}$/;a.exports={semver:d,git:e,commitId:f,
shortCommitId:g}},function(a,b,c){"use strict";function d(a){return k.string(a)&&m(a)}function e(a){return k.string(a)&&n(a)}function f(a){return k.string(a)&&(m(a)||n(a))}function g(a){return k.positiveNumber(a)&&a<=65535}function h(a){return k.positiveNumber(a)&&a<=1024}function i(a){return g(a)&&a>1024}function j(a){return k.string(a)&&/^.+@.+\..+$/.test(a)}var k=c(1),l=c(3),m=l.startsWith.bind(null,"http://"),n=l.startsWith.bind(null,"https://");a.exports={email:j,http:d,https:e,port:g,secure:e,systemPort:h,url:f,userPort:i,webUrl:f}},function(a,b,c){"use strict";function d(a,b){return h(g.object(a),"missing object to check"),h(g.object(b),"missing predicates object"),Object.keys(b).forEach(function(a){if(!g.fn(b[a]))throw new Error("not a predicate function for "+a+" but "+b[a])}),i(j(a,b))}function e(a,b){return d(b,a)}var f=c(2).curry2,g=c(1),h=c(4),i=c(6).every,j=c(6).map;h(g.fn(i),"missing check.every method"),h(g.fn(j),"missing check.map method"),a.exports={all:d,schema:f(e)}}]);
});