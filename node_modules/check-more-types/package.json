{"name": "check-more-types", "description": "Large collection of predicates.", "version": "2.24.0", "author": "<PERSON><PERSON><PERSON> <gleb.bah<PERSON><PERSON>@gmail.com>", "bugs": {"url": "https://github.com/kensho/check-more-types/issues"}, "config": {"pre-git": {"commit-msg": "simple", "pre-commit": ["npm run build", "npm test"], "pre-push": ["npm run size"], "post-commit": ["git status"], "post-merge": []}, "next-update": {"skip": ["grunt-cli"]}, "include-version": ["dist/check-more-types.js", "dist/check-more-types.min.js"]}, "contributors": [], "dependencies": {}, "devDependencies": {"@semantic-release/condition-travis": "5.0.2", "condition-node-version": "1.3.0", "coveralls": "2.11.8", "git-issues": "1.2.0", "grunt-cli": "0.1.13", "grunt-contrib-jshint": "1.0.0", "grunt-contrib-uglify": "1.0.0", "grunt-contrib-watch": "0.6.1", "grunt-deps-ok": "0.9.0", "grunt-gt": "0.1.6", "grunt-jshint-solid": "0.1.1", "grunt-mocha-test": "0.12.7", "grunt-nice-package": "0.10.3", "grunt-npm2bower-sync": "0.9.1", "grunt-readme": "0.4.5", "grunt-toc": "0.1.0", "grunt-xplain": "0.2.7", "include-version": "1.0.2", "jshint-summary": "0.4.0", "karma": "0.13.22", "karma-chrome-launcher": "0.2.3", "karma-mocha": "0.2.2", "karma-phantomjs-launcher": "1.0.0", "lazy-ass": "1.4.0", "lazy-ass-helpful": "0.6.1", "lcov-filter": "0.1.1", "lodash": "4.6.1", "matchdep": "1.0.1", "mocha": "3.2.0", "phantomjs-prebuilt": "2.1.7", "pkgfiles": "2.3.0", "pre-git": "3.4.1", "semantic-release": "6.3.6", "standard": "9.0.2", "webpack": "1.12.14"}, "engines": {"node": ">= 0.8.0"}, "files": ["bower.json", "dist/check-more-types.d.ts", "dist/check-more-types.js", "dist/check-more-types.min.js"], "homepage": "https://github.com/kensho/check-more-types", "keywords": ["check-types", "checks", "duck-typing", "type-checking", "types"], "license": "MIT", "main": "dist/check-more-types.js", "release": {"verifyConditions": [{"path": "@semantic-release/condition-travis"}, {"path": "condition-node-version", "node": "4.2.2"}]}, "repository": {"type": "git", "url": "https://github.com/kensho/check-more-types.git"}, "scripts": {"build": "npm run pack && grunt", "commit": "git-issues && commit-wizard", "coveralls": "lcov-filter cover/lcov.info test | ./node_modules/coveralls/bin/coveralls.js", "issues": "git-issues", "karma": "karma start test/karma.conf.js", "lint": "standard --verbose --fix src/*.js test/*.js", "mocha": "mocha src/*-spec.js test/check-more-types-spec.js", "node-test": "node test/load-under-node-test.js", "pack": "webpack", "pkgfiles": "pkgfiles", "pretest": "npm run lint && npm run build", "semantic-release": "semantic-release pre && include-version && npm publish && semantic-release post", "size": "tarball=\"$(npm pack .)\"; wc -c \"${tarball}\"; tar tvf \"${tarball}\"; rm \"${tarball}\";", "test": "npm run node-test && npm run mocha && npm run karma", "unit": "npm run mocha", "watch": "grunt watch"}, "types": "dist/check-more-types.d.ts"}