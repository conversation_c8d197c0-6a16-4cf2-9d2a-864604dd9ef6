{"name": "check-more-types", "main": "dist/check-more-types.js", "version": "0.0.0", "homepage": "https://github.com/kensho/check-more-types", "license": "MIT", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests", "docs", "Gruntfile.js", "package.json", "index.html"], "keywords": ["check-types", "checks", "duck-typing", "type-checking", "types"], "description": "Collection of predicates and type checks", "authors": ["<PERSON><PERSON><PERSON> <gleb.bah<PERSON><PERSON>@gmail.com>"]}