{"name": "@types/sinonjs__fake-timers", "version": "8.1.1", "description": "TypeScript definitions for @sinonjs/fake-timers", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/sinonjs__fake-timers", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/Nemo157", "githubUsername": "Nemo157"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/rogierschouten", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/zyishai", "githubUsername": "<PERSON><PERSON>sha<PERSON>"}, {"name": "Re<PERSON><PERSON>", "url": "https://github.com/remcohaszing", "githubUsername": "rem<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/JadenSimon", "githubUsername": "JadenSimon"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/sinonjs__fake-timers"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "0a9ab57777b64eca4778e09596f9082dc67e788bb0b64cc635671a3b2c9744a0", "typeScriptVersion": "3.8"}