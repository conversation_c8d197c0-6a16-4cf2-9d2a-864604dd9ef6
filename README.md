HYPER-AUTOMATION-TESTS
   This is a Cypress-based automation testing project designed to test a web application using the Page Object Model (POM) and APIs using a service-based pattern. The project is organized to support both UI and API test automation with a modular structure.
Overview

UI Tests: Located in cypress/e2e/uiTests/, these tests use POM with page objects defined in cypress/Pages/.
API Tests: Located in cypress/e2e/apiTests/, these tests use service modules defined in cypress/services/.
Fixtures: Mock data for tests is stored in cypress/fixtures/.
Support Files: Custom commands and global configurations are in cypress/support/.

Prerequisites

Node.js (version 12 or higher)
npm (usually comes with Node.js)
Git (for version control)

Setup

Clone the repository:git clone https://github.com/mvmhyper/hyper-automation-tests.git
cd hyper-automation-tests


Install dependencies:npm install


Ensure the web application and API are running (e.g., on http://localhost:3000 and your API base URL).
Configure environment variables in cypress.config.js if needed (e.g., apiUrl).

Running Tests

Open Cypress Test Runner (interactive mode):npx cypress open


Run All Tests (headless mode):npx cypress run


Run UI Tests Only:npx cypress run --spec 'cypress/e2e/uiTests/*.cy.js'


Run API Tests Only:npx cypress run --spec 'cypress/e2e/apiTests/*.cy.js'



Folder Structure
HYPER-AUTOMATION-TESTS/
├── cypress/
│   ├── e2e/
│   │   ├── apiTests/         # API test specs (e.g., auth.api.cy.js)
│   │   └── uiTests/          # UI test specs (e.g., signUp.ui.cy.js)
│   ├── fixtures/             # Mock data
│   ├── Pages/                # Page objects for UI tests (e.g., login.page.js)
│   ├── services/             # Service modules for API tests
│   ├── support/              # Custom commands and support files
│   └── cypress.config.js     # Cypress configuration
├── node_modules/             # Dependencies
├── package.json              # Project configuration
├── package-lock.json         # Dependency lock file
└── README.md                 # This file

Contributing

Add new page objects to cypress/Pages/ for UI tests.
Add new service modules to cypress/services/ for API tests.
Write test specs in cypress/e2e/uiTests/ or cypress/e2e/apiTests/.
Update fixtures in cypress/fixtures/ as needed.
Commit changes and push to the repository.

Notes

Ensure the baseUrl in cypress.config.js matches your web application's URL.
Update the apiUrl in cypress.config.js to point to your API endpoint.
Refer to cypress/support/commands.js for custom commands that can be reused in tests.
